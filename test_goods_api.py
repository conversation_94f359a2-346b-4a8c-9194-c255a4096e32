#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
货品API测试脚本
"""

import os
import sys
import json
from goods_api_client import WDTApiClient, GoodsExcelManager
from config import get_config

def test_api_connection():
    """测试API连接"""
    print("=== 测试API连接 ===")
    
    try:
        client = WDTApiClient()
        print(f"API客户端初始化成功")
        print(f"SID: {client.sid}")
        print(f"API URL: {client.api_url}")
        
        # 测试获取货品列表
        result = client.get_goods_list('北区京东POP店', 1, 5)
        print(f"API调用结果: {result.get('flag', 'unknown')}")
        
        if result.get('flag') == 'success':
            data = result.get('data', {})
            total_count = data.get('total_count', 0)
            goods_count = len(data.get('goods', []))
            print(f"总货品数量: {total_count}")
            print(f"当前页货品数量: {goods_count}")
            
            if goods_count > 0:
                first_goods = data['goods'][0]
                print(f"第一个货品信息: {json.dumps(first_goods, ensure_ascii=False, indent=2)}")
        else:
            print(f"API调用失败: {result.get('message', '')}")
            
    except Exception as e:
        print(f"API连接测试失败: {e}")
        return False
    
    return True

def test_excel_operations():
    """测试Excel操作"""
    print("\n=== 测试Excel操作 ===")
    
    try:
        excel_manager = GoodsExcelManager()
        print(f"Excel文件加载成功")
        print(f"数据行数: {len(excel_manager.df)}")
        print(f"列数: {len(excel_manager.df.columns)}")
        
        if not excel_manager.df.empty:
            print(f"列名: {list(excel_manager.df.columns)}")
            
            # 显示前几行数据
            print("\n前3行数据:")
            print(excel_manager.df.head(3).to_string())
            
            # 统计货主信息
            owners = excel_manager.df['货主'].value_counts()
            print(f"\n货主统计:")
            print(owners.head())
        
    except Exception as e:
        print(f"Excel操作测试失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置"""
    print("\n=== 测试配置 ===")
    
    try:
        config = get_config()
        print("配置加载成功:")
        print(f"  WDT SID: {config['wdt']['SID']}")
        print(f"  API URL: {config['wdt']['API_URL']}")
        print(f"  Excel文件: {config['excel']['FILE_PATH']}")
        print(f"  日志级别: {config['log']['LEVEL']}")
        print(f"  页面大小: {config['request']['PAGE_SIZE']}")
        print(f"  最大重试次数: {config['request']['MAX_RETRIES']}")
        
    except Exception as e:
        print(f"配置测试失败: {e}")
        return False
    
    return True

def test_field_mapping():
    """测试字段映射"""
    print("\n=== 测试字段映射 ===")
    
    try:
        config = get_config()
        api_to_excel = config['field_mapping']['api_to_excel']
        excel_to_api = config['field_mapping']['excel_to_api']
        
        print(f"API到Excel字段映射数量: {len(api_to_excel)}")
        print(f"Excel到API字段映射数量: {len(excel_to_api)}")
        
        print("\nAPI到Excel映射示例:")
        for i, (api_field, excel_field) in enumerate(api_to_excel.items()):
            if i < 5:  # 只显示前5个
                print(f"  {api_field} -> {excel_field}")
        
        print("\nExcel列定义:")
        excel_columns = config['excel_columns']
        print(f"  总列数: {len(excel_columns)}")
        print(f"  前10列: {excel_columns[:10]}")
        
    except Exception as e:
        print(f"字段映射测试失败: {e}")
        return False
    
    return True

def test_api_update():
    """测试API更新功能（仅测试，不实际更新）"""
    print("\n=== 测试API更新功能 ===")
    
    try:
        client = WDTApiClient()
        
        # 准备测试数据（使用无效的货品编号，避免实际更新）
        test_data = {
            'owner_no': 'test_owner',
            'spec_no': 'test_spec_no_not_exist',
            'length': '10',
            'width': '5',
            'height': '3',
            'volume': '150',
            'gross_weight': '0.5'
        }
        
        print(f"测试数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
        
        # 注意：这里会实际调用API，但使用不存在的货品编号
        result = client.update_goods_info(test_data)
        print(f"API更新测试结果: {result.get('flag', 'unknown')}")
        print(f"返回消息: {result.get('message', '')}")
        
    except Exception as e:
        print(f"API更新测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("货品API系统测试")
    print("=" * 50)
    
    tests = [
        ("配置测试", test_config),
        ("字段映射测试", test_field_mapping),
        ("Excel操作测试", test_excel_operations),
        ("API连接测试", test_api_connection),
        ("API更新测试", test_api_update),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"{test_name}执行异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接。")
        return 1

if __name__ == '__main__':
    sys.exit(main())
