#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试正确的API接口参数
"""

import hashlib
import time
import requests
import json
from datetime import datetime, timedelta

def test_goods_spec_query_step():
    """测试 goods.spec.query.step 接口（使用正确参数）"""
    print("测试 goods.spec.query.step 接口（使用正确参数）")
    print("=" * 60)
    
    # API配置
    SID = 'changhe'
    APP_KEY = 'changhe_chycchsjcjgj_wdt'
    APP_SECRET = '72a70a3bedc51497a2a3b9c229d7df69'
    API_URL = 'https://openapi.wdtwms.com/open_api/service.php'
    
    # 生成时间范围（最近1小时）
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=1)
    
    start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
    
    print(f"时间范围: {start_time_str} 到 {end_time_str}")
    
    # 业务参数（根据API文档）
    params = {
        'owner_no': '北区京东POP店',
        'start_time': start_time_str,
        'end_time': end_time_str,
        'page_no': '0',  # 从0开始
        'page_size': '10'  # 默认10，最大30
    }
    
    # 公共参数
    common_params = {
        'sid': SID,
        'appkey': APP_KEY,
        'timestamp': str(int(time.time())),
        'method': 'goods.spec.query.step',
        'format': 'json',
        'v': '1.0'
    }
    
    # 合并参数
    all_params = {**common_params, **params}
    
    # 生成签名
    sorted_params = sorted(all_params.items())
    param_str = ''.join([f"{k}{v}" for k, v in sorted_params])
    sign_str = APP_SECRET + param_str + APP_SECRET
    sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
    all_params['sign'] = sign
    
    print(f"\n请求参数:")
    for key, value in all_params.items():
        if key != 'sign':
            print(f"  {key}: {value}")
    print(f"  sign: {sign}")
    
    try:
        print(f"\n发送API请求...")
        response = requests.post(API_URL, data=all_params, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        print(f"响应状态: {result.get('flag', 'unknown')}")
        print(f"响应消息: {result.get('message', '')}")
        
        if result.get('flag') == 'success':
            print(f"\n✓ API调用成功!")
            
            content = result.get('content', [])
            total = result.get('total', 0)
            
            print(f"总记录数: {total}")
            print(f"当前页记录数: {len(content)}")
            
            if content:
                print(f"\n第一个货品信息:")
                first_item = content[0]
                for key, value in first_item.items():
                    if key != 'all_barcode':  # 跳过复杂的条码数组
                        print(f"  {key}: {value}")
                
                # 显示条码信息
                if 'all_barcode' in first_item:
                    barcodes = first_item['all_barcode']
                    print(f"  all_barcode: {len(barcodes)}个条码")
                    for i, barcode_info in enumerate(barcodes[:3]):  # 只显示前3个
                        print(f"    [{i+1}] {barcode_info.get('barcode', '')}")
                
                print(f"\n货品字段列表:")
                fields = list(first_item.keys())
                for i in range(0, len(fields), 4):
                    row_fields = fields[i:i+4]
                    print(f"  {', '.join(row_fields)}")
            else:
                print(f"\n当前时间范围内无数据更新")
                
        else:
            print(f"\n✗ API调用失败")
            print(f"完整响应:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
    except Exception as e:
        print(f"\n✗ 请求异常: {e}")
        import traceback
        traceback.print_exc()

def test_different_time_ranges():
    """测试不同的时间范围"""
    print(f"\n\n测试不同时间范围")
    print("=" * 60)
    
    time_ranges = [
        ("最近1小时", timedelta(hours=1)),
        ("最近30分钟", timedelta(minutes=30)),
        ("最近24小时", timedelta(hours=24)),  # 这个会分多次调用
    ]
    
    for desc, delta in time_ranges:
        print(f"\n测试 {desc}:")
        
        end_time = datetime.now()
        start_time = end_time - delta
        
        # 如果超过1小时，分批处理
        if delta.total_seconds() > 3600:
            print(f"  时间跨度超过1小时，需要分批处理")
            batch_count = int(delta.total_seconds() / 3600) + 1
            print(f"  需要 {batch_count} 次API调用")
            continue
        
        start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
        end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"  时间范围: {start_time_str} 到 {end_time_str}")
        
        # 简化的API调用测试
        try:
            from goods_api_client import WDTApiClient
            client = WDTApiClient()
            result = client.get_goods_list('北区京东POP店', start_time_str, end_time_str, 0, 5)
            
            if result.get('flag') == 'success':
                content = result.get('content', [])
                total = result.get('total', 0)
                print(f"  ✓ 成功 - 总记录: {total}, 当前页: {len(content)}")
            else:
                print(f"  ✗ 失败 - {result.get('message', '')}")
                
        except Exception as e:
            print(f"  ✗ 异常 - {e}")

def main():
    """主测试函数"""
    test_goods_spec_query_step()
    test_different_time_ranges()
    
    print(f"\n" + "=" * 60)
    print("测试完成")
    print("\n使用说明:")
    print("1. 如果API调用成功，说明接口和参数都正确")
    print("2. 如果返回空数据，可能是指定时间范围内没有数据更新")
    print("3. 可以尝试更大的时间范围或不同的货主")

if __name__ == '__main__':
    main()
