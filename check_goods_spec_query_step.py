#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门检查 goods.spec.query.step 接口状态的工具
"""

import hashlib
import time
import requests
import json
from datetime import datetime, timedelta

class GoodsSpecQueryStepChecker:
    """goods.spec.query.step 接口检查器"""
    
    def __init__(self):
        self.SID = 'changhe'
        self.APP_KEY = 'changhe_chycchsjcjgj_wdt'
        self.APP_SECRET = '72a70a3bedc51497a2a3b9c229d7df69'
        self.API_URL = 'https://openapi.wdtwms.com/open_api/service.php'
    
    def generate_sign(self, params):
        """生成API签名"""
        sorted_params = sorted(params.items())
        param_str = ''.join([f"{k}{v}" for k, v in sorted_params])
        sign_str = self.APP_SECRET + param_str + self.APP_SECRET
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
    
    def test_basic_connection(self):
        """测试基础API连接"""
        print("1. 测试基础API连接")
        print("-" * 40)
        
        # 测试系统时间接口
        params = {
            'sid': self.SID,
            'appkey': self.APP_KEY,
            'timestamp': str(int(time.time())),
            'method': 'system.time',
            'format': 'json',
            'v': '1.0'
        }
        
        params['sign'] = self.generate_sign(params)
        
        try:
            response = requests.post(self.API_URL, data=params, timeout=10)
            result = response.json()
            
            if result.get('flag') == 'success':
                print("✓ API基础连接正常")
                print(f"  服务器时间: {result.get('data', {}).get('time', '未知')}")
                return True
            else:
                print("✗ API基础连接失败")
                print(f"  错误: {result.get('message', '')}")
                return False
                
        except Exception as e:
            print("✗ API连接异常")
            print(f"  异常: {e}")
            return False
    
    def test_goods_spec_query_step(self):
        """测试 goods.spec.query.step 接口"""
        print("\n2. 测试 goods.spec.query.step 接口")
        print("-" * 40)
        
        # 生成时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        
        start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
        end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"时间范围: {start_time_str} 到 {end_time_str}")
        
        # 测试参数
        params = {
            'sid': self.SID,
            'appkey': self.APP_KEY,
            'timestamp': str(int(time.time())),
            'method': 'goods.spec.query.step',
            'format': 'json',
            'v': '1.0',
            'owner_no': '北区京东POP店',
            'start_time': start_time_str,
            'end_time': end_time_str,
            'page_no': '0',
            'page_size': '5'
        }
        
        params['sign'] = self.generate_sign(params)
        
        print(f"请求参数:")
        for key, value in params.items():
            if key != 'sign':
                print(f"  {key}: {value}")
        print(f"  sign: {params['sign']}")
        
        try:
            response = requests.post(self.API_URL, data=params, timeout=30)
            result = response.json()
            
            print(f"\nAPI响应:")
            print(f"  flag: {result.get('flag')}")
            print(f"  code: {result.get('code', '')}")
            print(f"  message: {result.get('message', '')}")
            
            if result.get('flag') == 'success':
                print("✓ 接口调用成功!")
                
                content = result.get('content', [])
                total = result.get('total', 0)
                
                print(f"  总记录数: {total}")
                print(f"  当前页记录数: {len(content)}")
                
                if content:
                    print(f"  第一条记录字段: {list(content[0].keys())}")
                
                return True, result
            else:
                print("✗ 接口调用失败")
                
                # 分析错误类型
                message = result.get('message', '')
                code = result.get('code', '')
                
                if '名字错误' in message or 'invalid-argument' in code:
                    print("  → 接口名称问题或参数错误")
                elif '未部署' in message or '暂不支持' in message:
                    print("  → 接口未部署或不支持")
                elif '权限' in message or 'permission' in message.lower():
                    print("  → 权限问题")
                else:
                    print(f"  → 其他问题: {message}")
                
                return False, result
                
        except Exception as e:
            print("✗ 接口调用异常")
            print(f"  异常: {e}")
            return False, None
    
    def test_different_owners(self):
        """测试不同货主"""
        print("\n3. 测试不同货主")
        print("-" * 40)
        
        owners = ['北区京东POP店', '七彩虹天猫店', '七彩虹新零售', '七彩虹速卖通']
        
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
        end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
        
        for owner in owners:
            print(f"\n测试货主: {owner}")
            
            params = {
                'sid': self.SID,
                'appkey': self.APP_KEY,
                'timestamp': str(int(time.time())),
                'method': 'goods.spec.query.step',
                'format': 'json',
                'v': '1.0',
                'owner_no': owner,
                'start_time': start_time_str,
                'end_time': end_time_str,
                'page_no': '0',
                'page_size': '3'
            }
            
            params['sign'] = self.generate_sign(params)
            
            try:
                response = requests.post(self.API_URL, data=params, timeout=10)
                result = response.json()
                
                if result.get('flag') == 'success':
                    content = result.get('content', [])
                    total = result.get('total', 0)
                    print(f"  ✓ 成功 - 总记录: {total}, 当前页: {len(content)}")
                else:
                    print(f"  ✗ 失败 - {result.get('message', '')}")
                    
            except Exception as e:
                print(f"  ✗ 异常 - {e}")
    
    def generate_support_request(self):
        """生成技术支持请求"""
        print("\n4. 技术支持请求模板")
        print("-" * 40)
        
        template = f"""
主题：goods.spec.query.step 接口部署状态咨询

您好，

我们在使用 goods.spec.query.step 接口时遇到问题，需要技术支持。

== 基本信息 ==
接口名称：goods.spec.query.step
API密钥：{self.APP_KEY}
SID：{self.SID}
API地址：{self.API_URL}

== 错误信息 ==
响应状态：failure
错误代码：client.protocol.invalid-argument
错误消息：接口【】名字错误

== 请求参数示例 ==
owner_no: 北区京东POP店
start_time: {(datetime.now() - timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S')}
end_time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
page_no: 0
page_size: 10

== 问题咨询 ==
1. 该接口是否已在我们的环境中部署？
2. 我们的API密钥是否有访问该接口的权限？
3. 是否需要特殊的配置或开通流程？
4. 预计什么时候可以解决？
5. 是否有替代的接口可以获取货品档案数据？

== 业务需求 ==
我们需要通过API获取WMS的单品档案资料，用于货品信息同步。
这是我们业务系统的核心功能，希望能尽快解决。

== 联系方式 ==
请回复此邮件或提供其他联系方式。

谢谢！
"""
        
        print(template)
        
        # 保存到文件
        with open('技术支持请求.txt', 'w', encoding='utf-8') as f:
            f.write(template)
        
        print(f"\n已保存技术支持请求模板到: 技术支持请求.txt")

def main():
    """主函数"""
    print("goods.spec.query.step 接口状态检查")
    print("=" * 60)
    
    checker = GoodsSpecQueryStepChecker()
    
    # 1. 测试基础连接
    basic_ok = checker.test_basic_connection()
    
    # 2. 测试目标接口
    interface_ok, result = checker.test_goods_spec_query_step()
    
    # 3. 如果基础连接正常但接口不可用，测试不同货主
    if basic_ok and not interface_ok:
        checker.test_different_owners()
    
    # 4. 生成技术支持请求
    checker.generate_support_request()
    
    # 总结
    print("\n" + "=" * 60)
    print("检查结果总结:")
    
    if basic_ok:
        print("✓ API基础连接正常")
    else:
        print("✗ API基础连接有问题")
        print("  建议检查网络连接和API配置")
        return
    
    if interface_ok:
        print("✓ goods.spec.query.step 接口可用")
        print("  程序可以正常使用")
    else:
        print("✗ goods.spec.query.step 接口不可用")
        print("  建议:")
        print("  1. 发送技术支持请求（已生成模板）")
        print("  2. 使用模拟模式继续开发:")
        print("     set API_MOCK_MODE=true")
        print("     python api_to_excel.py")

if __name__ == '__main__':
    main()
