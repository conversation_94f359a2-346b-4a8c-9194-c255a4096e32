#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试API接口 - 不依赖其他模块
"""

import hashlib
import time
import requests
import json

def test_api_methods():
    """测试多个可能的API方法"""
    print("测试多个可能的API接口")
    print("=" * 50)

    # API配置
    SID = 'changhe'
    APP_KEY = 'changhe_chycchsjcjgj_wdt'
    APP_SECRET = '72a70a3bedc51497a2a3b9c229d7df69'
    API_URL = 'https://openapi.wdtwms.com/open_api/service.php'

    # 业务参数
    params = {
        'owner_no': '北区京东POP店',
        'page_no': '1',
        'page_size': '5'
    }

    # 要测试的接口方法
    methods_to_test = [
        'goods.spec.query.step',
        'goods.spec.query',
        'goods.query',
        'goods.list',
        'goods.spec.list',
        'spec.query',
        'spec.list',
        'goods.info.query',
        'goods.search'
    ]

    for method in methods_to_test:
        print(f"\n测试接口: {method}")
        print("-" * 30)

        # 公共参数
        common_params = {
            'sid': SID,
            'appkey': APP_KEY,
            'timestamp': str(int(time.time())),
            'method': method,
            'format': 'json',
            'v': '1.0'
        }

        # 合并参数
        all_params = {**common_params, **params}

        # 生成签名
        sorted_params = sorted(all_params.items())
        param_str = ''.join([f"{k}{v}" for k, v in sorted_params])
        sign_str = APP_SECRET + param_str + APP_SECRET
        sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
        all_params['sign'] = sign

        try:
            response = requests.post(API_URL, data=all_params, timeout=10)
            response.raise_for_status()

            result = response.json()
            status = result.get('flag', 'unknown')
            message = result.get('message', '')

            if status == 'success':
                print(f"  ✓ 成功: {method}")
                data = result.get('data', {})

                if 'goods' in data:
                    goods_count = len(data['goods'])
                    print(f"    获取到 {goods_count} 个货品")

                if 'total_count' in data:
                    print(f"    总记录数: {data['total_count']}")

                # 显示第一个货品的字段
                if 'goods' in data and data['goods']:
                    first_goods = data['goods'][0]
                    print(f"    货品字段: {list(first_goods.keys())[:5]}...")

                return method  # 返回成功的方法名

            else:
                print(f"  ✗ 失败: {method} - {message}")

        except Exception as e:
            print(f"  ✗ 异常: {method} - {e}")

    return None

if __name__ == '__main__':
    successful_method = test_api_methods()

    if successful_method:
        print(f"\n🎉 找到可用的API接口: {successful_method}")
        print("请更新程序中的接口名称")
    else:
        print("\n❌ 未找到可用的API接口")
        print("请联系API提供方确认正确的接口名称")
