#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的API接口 goods.spec.query.step
"""

import os
import json
import hashlib
import time
import requests
from datetime import datetime

# API配置
SID = 'changhe'
APP_KEY = 'changhe_chycchsjcjgj_wdt'
APP_SECRET = '72a70a3bedc51497a2a3b9c229d7df69'
API_URL = 'https://openapi.wdtwms.com/open_api/service.php'

def generate_sign(params):
    """生成API签名"""
    # 按key排序参数
    sorted_params = sorted(params.items())
    
    # 拼接参数字符串
    param_str = ''.join([f"{k}{v}" for k, v in sorted_params])
    
    # 加上app_secret
    sign_str = APP_SECRET + param_str + APP_SECRET
    
    # MD5加密并转大写
    return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()

def make_api_request(method, params):
    """发送API请求"""
    # 公共参数
    common_params = {
        'sid': SID,
        'appkey': APP_KEY,
        'timestamp': str(int(time.time())),
        'method': method,
        'format': 'json',
        'v': '1.0'
    }
    
    # 合并参数
    all_params = {**common_params, **params}
    
    # 生成签名
    all_params['sign'] = generate_sign(all_params)
    
    print(f"发送API请求: {method}")
    print(f"请求参数: {all_params}")
    
    try:
        response = requests.post(API_URL, data=all_params, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        print(f"API响应状态: {result.get('flag', 'unknown')}")
        
        return result
        
    except requests.exceptions.RequestException as e:
        print(f"API请求失败: {e}")
        raise
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        raise

def test_goods_spec_query_step():
    """测试 goods.spec.query.step 接口"""
    print("=== 测试 goods.spec.query.step 接口 ===")
    
    params = {
        'owner_no': '北区京东POP店',
        'page_no': '1',
        'page_size': '5'
    }
    
    try:
        result = make_api_request('goods.spec.query.step', params)
        
        print(f"完整响应:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        if result.get('flag') == 'success':
            data = result.get('data', {})
            print(f"\n成功获取数据!")
            print(f"数据结构: {list(data.keys())}")
            
            if 'goods' in data:
                goods_list = data['goods']
                print(f"货品数量: {len(goods_list)}")
                
                if goods_list:
                    first_goods = goods_list[0]
                    print(f"第一个货品字段: {list(first_goods.keys())}")
            
            if 'total_count' in data:
                print(f"总记录数: {data['total_count']}")
                
        else:
            print(f"API调用失败: {result.get('message', '')}")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_different_methods():
    """测试不同的接口方法"""
    print("\n=== 测试不同接口方法 ===")
    
    methods_to_test = [
        'goods.spec.query.step',
        'goods.query',
        'goods.spec.query',
        'goods.list'
    ]
    
    params = {
        'owner_no': '北区京东POP店',
        'page_no': '1',
        'page_size': '3'
    }
    
    for method in methods_to_test:
        print(f"\n测试接口: {method}")
        try:
            result = make_api_request(method, params)
            
            if result.get('flag') == 'success':
                print(f"  ✓ 成功")
                data = result.get('data', {})
                if 'goods' in data:
                    print(f"  货品数量: {len(data['goods'])}")
                if 'total_count' in data:
                    print(f"  总记录数: {data['total_count']}")
            else:
                print(f"  ✗ 失败: {result.get('message', '')}")
                
        except Exception as e:
            print(f"  ✗ 异常: {e}")

def main():
    """主测试函数"""
    print("测试新的API接口")
    print("=" * 50)
    
    test_goods_spec_query_step()
    test_different_methods()
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == '__main__':
    main()
