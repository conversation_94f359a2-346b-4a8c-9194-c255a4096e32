#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
货品信息API客户端
通过API接口获取货品信息并写入Excel文档
"""

import os
import json
import hashlib
import time
import requests
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('goods_api.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class WDTApiClient:
    """旺店通API客户端"""
    
    def __init__(self):
        """初始化API客户端"""
        self.sid = os.getenv('WDT_SID', 'changhe')
        self.app_key = os.getenv('WDT_APP_KEY', 'changhe_chycchsjcjgj_wdt')
        self.app_secret = os.getenv('WDT_APP_SECRET', '72a70a3bedc51497a2a3b9c229d7df69')
        self.api_url = os.getenv('WDT_API_URL', 'https://openapi.wdtwms.com/open_api/service.php')
        
        logger.info(f"API客户端初始化完成 - SID: {self.sid}")
    
    def _generate_sign(self, params: Dict[str, Any]) -> str:
        """生成API签名"""
        # 按key排序参数
        sorted_params = sorted(params.items())
        
        # 拼接参数字符串
        param_str = ''.join([f"{k}{v}" for k, v in sorted_params])
        
        # 加上app_secret
        sign_str = self.app_secret + param_str + self.app_secret
        
        # MD5加密并转大写
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
    
    def _make_request(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """发送API请求"""
        # 公共参数
        common_params = {
            'sid': self.sid,
            'appkey': self.app_key,
            'timestamp': str(int(time.time())),
            'method': method,
            'format': 'json',
            'v': '1.0'
        }
        
        # 合并参数
        all_params = {**common_params, **params}
        
        # 生成签名
        all_params['sign'] = self._generate_sign(all_params)
        
        try:
            logger.info(f"发送API请求: {method}")
            response = requests.post(self.api_url, data=all_params, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"API响应: {result.get('flag', 'unknown')}")
            
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {e}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            raise
    
    def get_goods_list(self, owner_no: str, page_no: int = 1, page_size: int = 100) -> Dict[str, Any]:
        """获取货品列表"""
        params = {
            'owner_no': owner_no,
            'page_no': str(page_no),
            'page_size': str(page_size)
        }
        
        return self._make_request('goods.query', params)
    
    def update_goods_info(self, goods_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新货品信息"""
        return self._make_request('goods.info.update', goods_data)


class GoodsExcelManager:
    """货品Excel文档管理器"""
    
    def __init__(self, excel_path: str = '货品档案.xlsx'):
        """初始化Excel管理器"""
        self.excel_path = excel_path
        self.df = None
        self.load_excel()
    
    def load_excel(self):
        """加载Excel文件"""
        try:
            if os.path.exists(self.excel_path):
                self.df = pd.read_excel(self.excel_path)
                logger.info(f"成功加载Excel文件: {self.excel_path}, 共{len(self.df)}行数据")
            else:
                # 创建新的DataFrame
                self.df = pd.DataFrame(columns=[
                    '货主', '货品编号', '货品名称', '备注', '修改时间', '货品简称',
                    '货品别名', '分类编号', '分类名称', '品牌', '货品类别', '产地',
                    '标记名称', '自定义属性1', '自定义属性2', '自定义属性3',
                    '自定义属性4', '自定义属性5', '自定义属性6', '创建时间',
                    '长度(cm)', '宽度(cm)', '高度(cm)', '体积(cm³)', '毛重(kg)',
                    '基本单位', '每箱数量', '箱重量(kg)', '箱体积(cm³)', '保质期天数', '临期天数'
                ])
                logger.info(f"创建新的Excel文件结构")
        except Exception as e:
            logger.error(f"加载Excel文件失败: {e}")
            raise
    
    def save_excel(self):
        """保存Excel文件"""
        try:
            # 备份原文件
            if os.path.exists(self.excel_path):
                backup_path = f"{self.excel_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                os.rename(self.excel_path, backup_path)
                logger.info(f"原文件已备份为: {backup_path}")
            
            # 保存新文件
            self.df.to_excel(self.excel_path, index=False)
            logger.info(f"Excel文件已保存: {self.excel_path}")
            
        except Exception as e:
            logger.error(f"保存Excel文件失败: {e}")
            raise
    
    def update_goods_data(self, goods_list: List[Dict[str, Any]]):
        """更新货品数据到Excel"""
        updated_count = 0
        new_count = 0
        
        for goods in goods_list:
            try:
                # 查找现有记录
                mask = (self.df['货主'] == goods.get('owner_no', '')) & \
                       (self.df['货品编号'] == goods.get('spec_no', ''))
                
                existing_rows = self.df[mask]
                
                # 准备更新数据
                update_data = {
                    '货主': goods.get('owner_no', ''),
                    '货品编号': goods.get('spec_no', ''),
                    '货品名称': goods.get('goods_name', ''),
                    '修改时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    '长度(cm)': goods.get('length', ''),
                    '宽度(cm)': goods.get('width', ''),
                    '高度(cm)': goods.get('height', ''),
                    '体积(cm³)': goods.get('volume', ''),
                    '毛重(kg)': goods.get('gross_weight', ''),
                    '基本单位': goods.get('base_unit', ''),
                    '每箱数量': goods.get('unit_ratio', ''),
                    '箱重量(kg)': goods.get('box_weight', ''),
                    '箱体积(cm³)': goods.get('box_volume', ''),
                    '保质期天数': goods.get('validity_days', ''),
                    '临期天数': goods.get('expire_days', '')
                }
                
                if len(existing_rows) > 0:
                    # 更新现有记录
                    for col, value in update_data.items():
                        if col in self.df.columns and value:
                            self.df.loc[mask, col] = value
                    updated_count += 1
                    logger.debug(f"更新货品: {goods.get('spec_no', '')}")
                else:
                    # 添加新记录
                    if not update_data.get('创建时间'):
                        update_data['创建时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    
                    new_row = pd.DataFrame([update_data])
                    self.df = pd.concat([self.df, new_row], ignore_index=True)
                    new_count += 1
                    logger.debug(f"新增货品: {goods.get('spec_no', '')}")
                    
            except Exception as e:
                logger.error(f"处理货品数据失败 {goods.get('spec_no', '')}: {e}")
                continue
        
        logger.info(f"数据更新完成 - 更新: {updated_count}条, 新增: {new_count}条")
        return updated_count, new_count


def main():
    """主函数"""
    try:
        # 初始化API客户端和Excel管理器
        api_client = WDTApiClient()
        excel_manager = GoodsExcelManager()
        
        # 获取货主列表（从现有Excel中获取）
        owners = excel_manager.df['货主'].unique() if not excel_manager.df.empty else ['北区京东POP店']
        
        logger.info(f"开始处理 {len(owners)} 个货主的数据")
        
        total_updated = 0
        total_new = 0
        
        for owner in owners:
            if pd.isna(owner) or not owner:
                continue
                
            logger.info(f"处理货主: {owner}")
            
            try:
                # 获取货品列表
                page_no = 1
                while True:
                    result = api_client.get_goods_list(owner, page_no)
                    
                    if result.get('flag') != 'success':
                        logger.warning(f"获取货品列表失败: {result.get('message', '')}")
                        break
                    
                    goods_list = result.get('data', {}).get('goods', [])
                    if not goods_list:
                        break
                    
                    # 更新Excel数据
                    updated, new = excel_manager.update_goods_data(goods_list)
                    total_updated += updated
                    total_new += new
                    
                    # 检查是否还有更多页
                    total_count = result.get('data', {}).get('total_count', 0)
                    if page_no * 100 >= total_count:
                        break
                    
                    page_no += 1
                    
            except Exception as e:
                logger.error(f"处理货主 {owner} 时出错: {e}")
                continue
        
        # 保存Excel文件
        excel_manager.save_excel()
        
        logger.info(f"任务完成 - 总计更新: {total_updated}条, 新增: {total_new}条")
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        raise


if __name__ == '__main__':
    main()
