#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速API接口测试工具
"""

import hashlib
import time
import requests
import json

def quick_test_api(method_name):
    """快速测试单个API接口"""
    print(f"测试接口: {method_name}")
    
    # API配置
    SID = 'changhe'
    APP_KEY = 'changhe_chycchsjcjgj_wdt'
    APP_SECRET = '72a70a3bedc51497a2a3b9c229d7df69'
    API_URL = 'https://openapi.wdtwms.com/open_api/service.php'
    
    # 基本参数
    params = {
        'sid': SID,
        'appkey': APP_KEY,
        'timestamp': str(int(time.time())),
        'method': method_name,
        'format': 'json',
        'v': '1.0',
        'owner_no': '北区京东POP店'
    }
    
    # 生成签名
    sorted_params = sorted(params.items())
    param_str = ''.join([f"{k}{v}" for k, v in sorted_params])
    sign_str = APP_SECRET + param_str + APP_SECRET
    params['sign'] = hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
    
    try:
        response = requests.post(API_URL, data=params, timeout=10)
        result = response.json()
        
        flag = result.get('flag', 'unknown')
        message = result.get('message', '')
        
        if flag == 'success':
            print(f"  ✓ 成功: {message}")
            if 'content' in result:
                print(f"  └─ 返回 {len(result['content'])} 条数据")
            elif 'data' in result:
                print(f"  └─ 数据字段: {list(result['data'].keys())}")
            return True
        else:
            print(f"  ✗ 失败: {message}")
            return False
            
    except Exception as e:
        print(f"  ✗ 异常: {e}")
        return False

def main():
    """主函数"""
    print("快速API接口测试")
    print("=" * 40)
    
    # 要测试的接口列表
    test_methods = [
        'goods.query',
        'goods.list', 
        'goods.spec.query',
        'spec.query',
        'goods.info.query',
        'wms.goods.query',
        'goods.search'
    ]
    
    successful_methods = []
    
    for method in test_methods:
        if quick_test_api(method):
            successful_methods.append(method)
    
    print("\n" + "=" * 40)
    if successful_methods:
        print(f"找到 {len(successful_methods)} 个可用接口:")
        for method in successful_methods:
            print(f"  ✓ {method}")
        
        # 更新配置建议
        best_method = successful_methods[0]
        print(f"\n建议更新配置:")
        print(f"在 config.py 中修改:")
        print(f"  'GOODS_QUERY': '{best_method}'")
        
    else:
        print("未找到可用接口")
        print("\n建议:")
        print("1. 联系API提供方确认接口名称")
        print("2. 使用模拟模式测试程序功能:")
        print("   set API_MOCK_MODE=true")
        print("   python api_to_excel.py")

if __name__ == '__main__':
    main()
