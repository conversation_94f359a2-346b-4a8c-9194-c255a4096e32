#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
货品信息API到Excel同步工具
简化版 - 仅支持从API获取数据并写入Excel
"""

import sys
import argparse
import time
import pandas as pd
from typing import List, Dict, Any
import logging
from config import get_config
from goods_api_client import WDTApiClient, GoodsExcelManager

# 配置日志
config = get_config()
logging.basicConfig(
    level=getattr(logging, config['log']['LEVEL']),
    format=config['log']['FORMAT'],
    handlers=[
        logging.FileHandler(config['log']['FILE_PATH'], encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ApiToExcelSync:
    """API到Excel同步器"""
    
    def __init__(self):
        """初始化同步器"""
        self.config = get_config()
        self.api_client = WDTApiClient()
        self.excel_manager = GoodsExcelManager(self.config['excel']['FILE_PATH'])
        logger.info("API到Excel同步器初始化完成")
    
    def sync_all_owners(self, owner_list: List[str] = None) -> Dict[str, int]:
        """同步所有指定货主的数据"""
        logger.info("开始从API同步数据到Excel")
        
        # 获取货主列表
        if not owner_list:
            if not self.excel_manager.df.empty:
                owner_list = self.excel_manager.df['货主'].dropna().unique().tolist()
            else:
                owner_list = ['北区京东POP店']  # 默认货主
        
        logger.info(f"处理货主列表: {owner_list}")
        
        total_stats = {'updated': 0, 'new': 0, 'errors': 0}
        
        for owner in owner_list:
            if not owner or str(owner).strip() == '':
                continue
                
            logger.info(f"开始处理货主: {owner}")
            
            try:
                owner_stats = self._sync_single_owner(owner)
                for key in total_stats:
                    total_stats[key] += owner_stats.get(key, 0)
                    
                logger.info(f"货主 {owner} 处理完成 - "
                           f"更新: {owner_stats['updated']}条, "
                           f"新增: {owner_stats['new']}条")
                    
            except Exception as e:
                logger.error(f"处理货主 {owner} 时出错: {e}")
                total_stats['errors'] += 1
                continue
        
        # 保存Excel文件
        try:
            self.excel_manager.save_excel()
            logger.info("Excel文件保存成功")
        except Exception as e:
            logger.error(f"保存Excel文件失败: {e}")
            total_stats['errors'] += 1
        
        logger.info(f"同步完成 - 总计更新: {total_stats['updated']}条, "
                   f"新增: {total_stats['new']}条, 错误: {total_stats['errors']}个")
        
        return total_stats
    
    def _sync_single_owner(self, owner: str, start_time: str = None, end_time: str = None) -> Dict[str, int]:
        """同步单个货主的货品数据"""
        from datetime import datetime, timedelta

        stats = {'updated': 0, 'new': 0, 'errors': 0}
        page_no = 0  # API文档显示从0开始
        page_size = min(self.config['request']['PAGE_SIZE'], 30)  # 最大30

        # 如果没有指定时间范围，使用最近1小时
        if not start_time or not end_time:
            end_dt = datetime.now()
            start_dt = end_dt - timedelta(hours=1)
            start_time = start_dt.strftime('%Y-%m-%d %H:%M:%S')
            end_time = end_dt.strftime('%Y-%m-%d %H:%M:%S')

        logger.info(f"同步时间范围: {start_time} 到 {end_time}")

        while True:
            try:
                # 获取货品列表
                logger.debug(f"获取货主 {owner} 第 {page_no} 页数据")
                result = self._retry_api_call(
                    self.api_client.get_goods_list,
                    owner, start_time, end_time, page_no, page_size
                )
                
                if result.get('flag') != 'success':
                    error_msg = result.get('message', '未知错误')
                    logger.warning(f"获取货品列表失败: {error_msg}")

                    # 如果是接口名称错误，给出提示
                    if '接口' in error_msg and '名字错误' in error_msg:
                        logger.error(f"API接口名称可能不正确，当前使用: goods.spec.query.step")
                        logger.error(f"请联系API提供方确认正确的接口名称")

                    if page_no == 1:  # 第一页就失败，记录错误
                        stats['errors'] += 1
                    break
                
                # API响应格式：content数组包含货品数据，total仅在page_no=0时返回
                content = result.get('content', [])
                total_count = result.get('total', 0) if page_no == 0 else None

                if not content:
                    logger.debug(f"货主 {owner} 第 {page_no} 页无数据")
                    break

                # 更新Excel数据
                updated, new = self.excel_manager.update_goods_data(content)
                stats['updated'] += updated
                stats['new'] += new

                logger.debug(f"货主 {owner} 第 {page_no} 页处理完成 - "
                           f"更新: {updated}条, 新增: {new}条")

                # 检查是否还有更多页
                if total_count is not None:
                    logger.debug(f"货主 {owner} 总记录数: {total_count}")

                # 如果当前页数据少于页面大小，说明已经是最后一页
                if len(content) < page_size:
                    logger.debug(f"货主 {owner} 数据获取完成")
                    break

                page_no += 1
                
                # 添加延迟避免请求过快
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"处理货主 {owner} 第 {page_no} 页时出错: {e}")
                stats['errors'] += 1
                break
        
        return stats
    
    def _retry_api_call(self, func, *args, **kwargs):
        """重试API调用"""
        max_retries = self.config['request']['MAX_RETRIES']
        retry_delay = self.config['request']['RETRY_DELAY']
        
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                logger.warning(f"API调用失败，{retry_delay}秒后重试 (第{attempt+1}次): {e}")
                time.sleep(retry_delay)
    
    def get_sync_summary(self) -> Dict[str, Any]:
        """获取同步摘要信息"""
        if self.excel_manager.df.empty:
            return {
                'total_goods': 0,
                'owners': [],
                'has_api_data': 0
            }
        
        df = self.excel_manager.df
        api_fields = ['长度(cm)', '宽度(cm)', '高度(cm)', '体积(cm³)', '毛重(kg)']
        has_api_data = df[api_fields].notna().any(axis=1).sum()
        
        return {
            'total_goods': len(df),
            'owners': df['货主'].value_counts().to_dict(),
            'has_api_data': has_api_data
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='货品信息API到Excel同步工具')
    parser.add_argument('--owners', nargs='+', help='指定货主列表，如：--owners "北区京东POP店" "七彩虹天猫店"')
    parser.add_argument('--start-time', help='开始时间，格式：YYYY-MM-DD HH:MM:SS')
    parser.add_argument('--end-time', help='结束时间，格式：YYYY-MM-DD HH:MM:SS')
    parser.add_argument('--config-check', action='store_true', help='检查配置信息')
    parser.add_argument('--summary', action='store_true', help='显示当前数据摘要')
    
    args = parser.parse_args()
    
    try:
        if args.config_check:
            config = get_config()
            print("配置信息:")
            print(f"  API URL: {config['wdt']['API_URL']}")
            print(f"  SID: {config['wdt']['SID']}")
            print(f"  Excel文件: {config['excel']['FILE_PATH']}")
            print(f"  日志文件: {config['log']['FILE_PATH']}")
            print(f"  页面大小: {config['request']['PAGE_SIZE']}")
            print(f"  最大重试次数: {config['request']['MAX_RETRIES']}")
            return
        
        sync_tool = ApiToExcelSync()
        
        if args.summary:
            summary = sync_tool.get_sync_summary()
            print("数据摘要:")
            print(f"  总货品数量: {summary['total_goods']}")
            print(f"  包含API数据的货品: {summary['has_api_data']}")
            print("  货主分布:")
            for owner, count in summary['owners'].items():
                print(f"    {owner}: {count}条")
            return
        
        # 验证时间参数
        if args.start_time and args.end_time:
            from datetime import datetime
            try:
                start_dt = datetime.strptime(args.start_time, '%Y-%m-%d %H:%M:%S')
                end_dt = datetime.strptime(args.end_time, '%Y-%m-%d %H:%M:%S')

                # 检查时间跨度不超过1小时
                time_diff = (end_dt - start_dt).total_seconds()
                if time_diff > 3600:
                    print("错误：时间跨度不能超过1小时")
                    return
                elif time_diff <= 0:
                    print("错误：结束时间必须大于开始时间")
                    return

                logger.info(f"使用指定时间范围: {args.start_time} 到 {args.end_time}")
            except ValueError as e:
                print(f"错误：时间格式不正确，请使用 YYYY-MM-DD HH:MM:SS 格式")
                return

        # 执行同步
        logger.info("开始执行API到Excel同步")
        stats = sync_tool.sync_all_owners(args.owners, args.start_time, args.end_time)
        
        print("同步完成！")
        print(f"  更新: {stats['updated']}条")
        print(f"  新增: {stats['new']}条")
        print(f"  错误: {stats['errors']}个")
        
        if stats['errors'] > 0:
            print("请查看日志文件了解错误详情")
        
        logger.info("程序执行完成")
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        print("程序被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
