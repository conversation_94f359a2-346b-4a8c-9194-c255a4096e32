#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
货品信息同步主程序
支持从API获取数据并同步到Excel，也支持从Excel更新数据到API
"""

import sys
import argparse
import time
import pandas as pd
from typing import List, Dict, Any
import logging
from config import get_config
from goods_api_client import WDTApiClient, GoodsExcelManager

# 配置日志
config = get_config()
logging.basicConfig(
    level=getattr(logging, config['log']['LEVEL']),
    format=config['log']['FORMAT'],
    handlers=[
        logging.FileHandler(config['log']['FILE_PATH'], encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class GoodsSyncManager:
    """货品同步管理器"""
    
    def __init__(self):
        """初始化同步管理器"""
        self.config = get_config()
        self.api_client = WDTApiClient()
        self.excel_manager = GoodsExcelManager(self.config['excel']['FILE_PATH'])
        
    def sync_from_api_to_excel(self, owner_list: List[str] = None) -> Dict[str, int]:
        """从API同步数据到Excel"""
        logger.info("开始从API同步数据到Excel")
        
        # 获取货主列表
        if not owner_list:
            if not self.excel_manager.df.empty:
                owner_list = self.excel_manager.df['货主'].dropna().unique().tolist()
            else:
                owner_list = ['北区京东POP店']  # 默认货主
        
        logger.info(f"处理货主列表: {owner_list}")
        
        total_stats = {'updated': 0, 'new': 0, 'errors': 0}
        
        for owner in owner_list:
            if not owner or str(owner).strip() == '':
                continue
                
            logger.info(f"处理货主: {owner}")
            
            try:
                owner_stats = self._sync_owner_goods(owner)
                for key in total_stats:
                    total_stats[key] += owner_stats.get(key, 0)
                    
            except Exception as e:
                logger.error(f"处理货主 {owner} 时出错: {e}")
                total_stats['errors'] += 1
                continue
        
        # 保存Excel文件
        try:
            self.excel_manager.save_excel()
            logger.info("Excel文件保存成功")
        except Exception as e:
            logger.error(f"保存Excel文件失败: {e}")
            total_stats['errors'] += 1
        
        logger.info(f"同步完成 - 更新: {total_stats['updated']}条, "
                   f"新增: {total_stats['new']}条, 错误: {total_stats['errors']}个")
        
        return total_stats
    
    def _sync_owner_goods(self, owner: str) -> Dict[str, int]:
        """同步单个货主的货品数据"""
        stats = {'updated': 0, 'new': 0, 'errors': 0}
        page_no = 1
        page_size = self.config['request']['PAGE_SIZE']
        
        while True:
            try:
                # 获取货品列表
                result = self._retry_api_call(
                    self.api_client.get_goods_list,
                    owner, page_no, page_size
                )
                
                if result.get('flag') != 'success':
                    logger.warning(f"获取货品列表失败: {result.get('message', '')}")
                    break
                
                data = result.get('data', {})
                goods_list = data.get('goods', [])
                
                if not goods_list:
                    logger.info(f"货主 {owner} 第 {page_no} 页无数据")
                    break
                
                # 更新Excel数据
                updated, new = self.excel_manager.update_goods_data(goods_list)
                stats['updated'] += updated
                stats['new'] += new
                
                logger.info(f"货主 {owner} 第 {page_no} 页处理完成 - "
                           f"更新: {updated}条, 新增: {new}条")
                
                # 检查是否还有更多页
                total_count = data.get('total_count', 0)
                if page_no * page_size >= total_count:
                    break
                
                page_no += 1
                
                # 添加延迟避免请求过快
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"处理货主 {owner} 第 {page_no} 页时出错: {e}")
                stats['errors'] += 1
                break
        
        return stats
    
    def sync_from_excel_to_api(self, owner_list: List[str] = None) -> Dict[str, int]:
        """从Excel同步数据到API"""
        logger.info("开始从Excel同步数据到API")
        
        if self.excel_manager.df.empty:
            logger.warning("Excel文件为空，无数据可同步")
            return {'updated': 0, 'errors': 0}
        
        # 筛选需要同步的数据
        df = self.excel_manager.df.copy()
        if owner_list:
            df = df[df['货主'].isin(owner_list)]
        
        logger.info(f"准备同步 {len(df)} 条记录到API")
        
        stats = {'updated': 0, 'errors': 0}
        
        for index, row in df.iterrows():
            try:
                # 准备API数据
                api_data = self._prepare_api_data(row)
                
                if not api_data.get('owner_no') or not api_data.get('spec_no'):
                    logger.warning(f"第 {index+1} 行数据缺少必要字段，跳过")
                    continue
                
                # 调用API更新
                result = self._retry_api_call(
                    self.api_client.update_goods_info,
                    api_data
                )
                
                if result.get('flag') == 'success':
                    stats['updated'] += 1
                    logger.debug(f"成功更新货品: {api_data.get('spec_no')}")
                else:
                    stats['errors'] += 1
                    logger.error(f"更新货品失败 {api_data.get('spec_no')}: "
                               f"{result.get('message', '')}")
                
                # 添加延迟避免请求过快
                time.sleep(0.2)
                
            except Exception as e:
                stats['errors'] += 1
                logger.error(f"处理第 {index+1} 行数据时出错: {e}")
                continue
        
        logger.info(f"API同步完成 - 更新: {stats['updated']}条, 错误: {stats['errors']}个")
        return stats
    
    def _prepare_api_data(self, row) -> Dict[str, Any]:
        """准备API数据"""
        field_mapping = self.config['field_mapping']['excel_to_api']
        api_data = {}
        
        for excel_col, api_field in field_mapping.items():
            if excel_col in row and not pd.isna(row[excel_col]):
                value = row[excel_col]
                if value != '' and str(value).strip() != '':
                    api_data[api_field] = str(value).strip()
        
        return api_data
    
    def _retry_api_call(self, func, *args, **kwargs):
        """重试API调用"""
        max_retries = self.config['request']['MAX_RETRIES']
        retry_delay = self.config['request']['RETRY_DELAY']
        
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                logger.warning(f"API调用失败，{retry_delay}秒后重试 (第{attempt+1}次): {e}")
                time.sleep(retry_delay)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='货品信息同步工具')
    parser.add_argument('--mode', choices=['api2excel', 'excel2api', 'both'], 
                       default='api2excel', help='同步模式')
    parser.add_argument('--owners', nargs='+', help='指定货主列表')
    parser.add_argument('--config-check', action='store_true', help='检查配置')
    
    args = parser.parse_args()
    
    try:
        if args.config_check:
            config = get_config()
            print("配置信息:")
            print(f"API URL: {config['wdt']['API_URL']}")
            print(f"SID: {config['wdt']['SID']}")
            print(f"Excel文件: {config['excel']['FILE_PATH']}")
            return
        
        sync_manager = GoodsSyncManager()
        
        if args.mode in ['api2excel', 'both']:
            logger.info("执行 API -> Excel 同步")
            stats = sync_manager.sync_from_api_to_excel(args.owners)
            print(f"API->Excel 同步结果: {stats}")
        
        if args.mode in ['excel2api', 'both']:
            logger.info("执行 Excel -> API 同步")
            stats = sync_manager.sync_from_excel_to_api(args.owners)
            print(f"Excel->API 同步结果: {stats}")
        
        logger.info("程序执行完成")
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
