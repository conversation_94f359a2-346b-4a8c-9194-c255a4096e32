#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
货品信息同步主程序
支持从API获取数据并同步到Excel，也支持从Excel更新数据到API
"""

import sys
import argparse
import time
import pandas as pd
from typing import List, Dict, Any
import logging
from config import get_config
from goods_api_client import WDTApiClient, GoodsExcelManager

# 配置日志
config = get_config()
logging.basicConfig(
    level=getattr(logging, config['log']['LEVEL']),
    format=config['log']['FORMAT'],
    handlers=[
        logging.FileHandler(config['log']['FILE_PATH'], encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class GoodsSyncManager:
    """货品同步管理器 - 仅支持API到Excel的单向同步"""

    def __init__(self):
        """初始化同步管理器"""
        self.config = get_config()
        self.api_client = WDTApiClient()
        self.excel_manager = GoodsExcelManager(self.config['excel']['FILE_PATH'])

    def sync_from_api_to_excel(self, owner_list: List[str] = None) -> Dict[str, int]:
        """从API同步数据到Excel"""
        logger.info("开始从API同步数据到Excel")
        
        # 获取货主列表
        if not owner_list:
            if not self.excel_manager.df.empty:
                owner_list = self.excel_manager.df['货主'].dropna().unique().tolist()
            else:
                owner_list = ['北区京东POP店']  # 默认货主
        
        logger.info(f"处理货主列表: {owner_list}")
        
        total_stats = {'updated': 0, 'new': 0, 'errors': 0}
        
        for owner in owner_list:
            if not owner or str(owner).strip() == '':
                continue
                
            logger.info(f"处理货主: {owner}")
            
            try:
                owner_stats = self._sync_owner_goods(owner)
                for key in total_stats:
                    total_stats[key] += owner_stats.get(key, 0)
                    
            except Exception as e:
                logger.error(f"处理货主 {owner} 时出错: {e}")
                total_stats['errors'] += 1
                continue
        
        # 保存Excel文件
        try:
            self.excel_manager.save_excel()
            logger.info("Excel文件保存成功")
        except Exception as e:
            logger.error(f"保存Excel文件失败: {e}")
            total_stats['errors'] += 1
        
        logger.info(f"同步完成 - 更新: {total_stats['updated']}条, "
                   f"新增: {total_stats['new']}条, 错误: {total_stats['errors']}个")
        
        return total_stats
    
    def _sync_owner_goods(self, owner: str) -> Dict[str, int]:
        """同步单个货主的货品数据"""
        stats = {'updated': 0, 'new': 0, 'errors': 0}
        page_no = 1
        page_size = self.config['request']['PAGE_SIZE']
        
        while True:
            try:
                # 获取货品列表
                result = self._retry_api_call(
                    self.api_client.get_goods_list,
                    owner, page_no, page_size
                )
                
                if result.get('flag') != 'success':
                    logger.warning(f"获取货品列表失败: {result.get('message', '')}")
                    break
                
                data = result.get('data', {})
                goods_list = data.get('goods', [])
                
                if not goods_list:
                    logger.info(f"货主 {owner} 第 {page_no} 页无数据")
                    break
                
                # 更新Excel数据
                updated, new = self.excel_manager.update_goods_data(goods_list)
                stats['updated'] += updated
                stats['new'] += new
                
                logger.info(f"货主 {owner} 第 {page_no} 页处理完成 - "
                           f"更新: {updated}条, 新增: {new}条")
                
                # 检查是否还有更多页
                total_count = data.get('total_count', 0)
                if page_no * page_size >= total_count:
                    break
                
                page_no += 1
                
                # 添加延迟避免请求过快
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"处理货主 {owner} 第 {page_no} 页时出错: {e}")
                stats['errors'] += 1
                break
        
        return stats
    
    def _retry_api_call(self, func, *args, **kwargs):
        """重试API调用"""
        max_retries = self.config['request']['MAX_RETRIES']
        retry_delay = self.config['request']['RETRY_DELAY']
        
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                logger.warning(f"API调用失败，{retry_delay}秒后重试 (第{attempt+1}次): {e}")
                time.sleep(retry_delay)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='货品信息同步工具 - API到Excel单向同步')
    parser.add_argument('--owners', nargs='+', help='指定货主列表')
    parser.add_argument('--config-check', action='store_true', help='检查配置')

    args = parser.parse_args()

    try:
        if args.config_check:
            config = get_config()
            print("配置信息:")
            print(f"API URL: {config['wdt']['API_URL']}")
            print(f"SID: {config['wdt']['SID']}")
            print(f"Excel文件: {config['excel']['FILE_PATH']}")
            return

        sync_manager = GoodsSyncManager()

        logger.info("执行 API -> Excel 同步")
        stats = sync_manager.sync_from_api_to_excel(args.owners)
        print(f"同步结果: {stats}")

        logger.info("程序执行完成")

    except KeyboardInterrupt:
        logger.info("用户中断程序")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
