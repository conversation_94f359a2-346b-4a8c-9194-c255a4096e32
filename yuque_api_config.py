#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语雀(Yu<PERSON>)新接口配置
"""

import os

# 语雀API配置
YUQUE_CONFIG = {
    'API_URL': 'https://yuque.com/api/v2',  # 语雀API地址
    'TOKEN': os.getenv('YUQUE_TOKEN', ''),  # 语雀访问令牌
    'NAMESPACE': os.getenv('YUQUE_NAMESPACE', ''),  # 知识库命名空间
    'TIMEOUT': 30
}

# 如果是其他基于yuque.com的API服务
CUSTOM_YUQUE_CONFIG = {
    'API_URL': 'https://api.yuque.com/goods',  # 自定义API地址
    'SID': os.getenv('YUQUE_SID', 'changhe'),
    'APP_KEY': os.getenv('YUQUE_APP_KEY', 'changhe_chycchsjcjgj_wdt'),
    'APP_SECRET': os.getenv('YUQUE_APP_SECRET', '72a70a3bedc51497a2a3b9c229d7df69'),
    'TIMEOUT': 30
}

# API接口映射
YUQUE_API_METHODS = {
    'GOODS_QUERY': 'goods.spec.query.step',
    'GOODS_UPDATE': 'goods.info.update'
}

def get_yuque_config():
    """获取语雀API配置"""
    return {
        'yuque': YUQUE_CONFIG.copy(),
        'custom_yuque': CUSTOM_YUQUE_CONFIG.copy(),
        'api_methods': YUQUE_API_METHODS.copy()
    }
