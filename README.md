# 货品档案API同步工具

这是一个用于通过API接口获取货品信息并写入Excel文档的Python工具。

## 功能特性

- 📥 从API获取货品信息并写入Excel
- 📊 自动备份Excel文件
- 🔁 支持重试机制和错误处理
- 📝 详细的日志记录
- ⚙️ 灵活的配置管理
- 🎯 支持指定货主范围
- 📈 数据统计和摘要

## 文件结构

```
货品档案/
├── api_to_excel.py        # 简化版主程序（推荐使用）
├── goods_api_client.py    # API客户端和Excel管理器
├── goods_sync.py          # 完整版同步程序
├── config.py              # 配置文件
├── requirements.txt       # Python依赖
├── README.md             # 使用说明
├── 货品档案.xlsx          # Excel数据文件
└── goods_api.log         # 日志文件
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

### API配置
程序使用以下API配置（在config.py中）：
- SID: changhe
- APP_KEY: changhe_chycchsjcjgj_wdt
- APP_SECRET: 72a70a3bedc51497a2a3b9c229d7df69
- API_URL: https://openapi.wdtwms.com/open_api/service.php

### 环境变量（可选）
可以通过环境变量覆盖默认配置：
```bash
export WDT_SID=your_sid
export WDT_APP_KEY=your_app_key
export WDT_APP_SECRET=your_app_secret
export WDT_API_URL=your_api_url
```

## 使用方法

### 1. 基本用法（推荐使用简化版）

#### 从API同步到Excel
```bash
python api_to_excel.py
```

#### 指定货主同步
```bash
python api_to_excel.py --owners "北区京东POP店" "七彩虹天猫店"
```

#### 检查配置
```bash
python api_to_excel.py --config-check
```

#### 查看数据摘要
```bash
python api_to_excel.py --summary
```

### 2. 完整版程序（保留双向功能）

```bash
# 仅API到Excel同步
python goods_sync.py

# 检查配置
python goods_sync.py --config-check
```

### 4. 直接使用API客户端

```python
from goods_api_client import WDTApiClient, GoodsExcelManager

# 初始化客户端
api_client = WDTApiClient()
excel_manager = GoodsExcelManager()

# 获取货品列表
result = api_client.get_goods_list('北区京东POP店')

# 更新货品信息
goods_data = {
    'owner_no': 'test',
    'spec_no': 'ceshi01',
    'length': '10',
    'width': '5',
    'height': '3'
}
result = api_client.update_goods_info(goods_data)
```

## Excel字段说明

### 原有字段
- 货主、货品编号、货品名称、备注、修改时间
- 货品简称、货品别名、分类编号、分类名称、品牌
- 货品类别、产地、标记名称
- 自定义属性1-6、创建时间

### 新增字段（API同步）
- 长度(cm)、宽度(cm)、高度(cm)
- 体积(cm³)、毛重(kg)
- 基本单位、每箱数量
- 箱重量(kg)、箱体积(cm³)
- 保质期天数、临期天数
- 条形码

## API接口说明

### 支持的接口

1. **goods.query** - 查询货品列表
   - 参数：owner_no, page_no, page_size
   - 返回：货品列表数据

2. **goods.info.update** - 更新货品信息
   - 参数：owner_no, spec_no, length, width, height等
   - 返回：更新结果

### 请求参数

| 参数名 | 类型 | 必须 | 说明 |
|--------|------|------|------|
| owner_no | string | 是 | 货主编号 |
| spec_no | string | 否 | 商家编码 |
| barcode | string | 否 | 条形码 |
| length | decimal | 否 | 长度(cm) |
| width | decimal | 否 | 宽度(cm) |
| height | decimal | 否 | 高度(cm) |
| volume | decimal | 否 | 体积(cm³) |
| gross_weight | decimal | 否 | 毛重(kg) |
| base_unit | string | 否 | 基本单位 |
| unit_ratio | decimal | 否 | 每箱数量 |
| box_weight | decimal | 否 | 箱重量(kg) |
| box_volume | decimal | 否 | 箱体积(cm³) |
| validity_days | decimal | 否 | 保质期天数 |
| expire_days | decimal | 否 | 临期天数 |

## 错误处理

- 自动重试机制（默认3次）
- 详细的错误日志记录
- Excel文件自动备份
- 网络超时处理
- 数据验证和清洗

## 日志文件

程序运行时会生成 `goods_api.log` 日志文件，包含：
- API请求和响应信息
- 数据处理过程
- 错误和警告信息
- 统计结果

## 注意事项

1. **数据备份**：程序会自动备份原Excel文件
2. **API限制**：注意API调用频率限制，程序已加入延迟机制
3. **数据完整性**：确保货主编号和货品编号的准确性
4. **网络环境**：确保能够访问API服务器
5. **权限要求**：确保API密钥有相应的操作权限

## 故障排除

### 常见问题

1. **API认证失败**
   - 检查SID、APP_KEY、APP_SECRET是否正确
   - 确认API密钥是否有效

2. **Excel文件无法打开**
   - 检查文件是否被其他程序占用
   - 确认文件路径是否正确

3. **网络连接问题**
   - 检查网络连接
   - 确认API服务器地址是否正确

4. **数据同步不完整**
   - 查看日志文件了解详细错误信息
   - 检查数据格式是否符合要求

### 调试模式

修改config.py中的日志级别为DEBUG：
```python
LOG_CONFIG = {
    'LEVEL': 'DEBUG',  # 改为DEBUG
    ...
}
```

## 版本历史

- v1.0.0: 初始版本，支持基本的API同步功能
- 支持双向同步
- 添加重试机制和错误处理
- 完善日志记录和配置管理
