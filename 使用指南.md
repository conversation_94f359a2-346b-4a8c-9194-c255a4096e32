# 货品档案API同步工具使用指南

## 快速开始

### 1. 环境准备
确保已安装Python 3.7+，然后安装依赖：
```bash
pip install -r requirements.txt
```

### 2. 配置检查
运行配置检查确保设置正确：
```bash
python goods_sync.py --config-check
```

### 3. 基本使用

#### 从API同步到Excel（推荐）
```bash
python goods_sync.py
```

#### 从Excel同步到API
```bash
python goods_sync.py --mode excel2api
```

#### 指定货主同步
```bash
python goods_sync.py --owners "北区京东POP店" "七彩虹天猫店"
```

## 文件说明

| 文件名 | 说明 |
|--------|------|
| `goods_sync.py` | 主程序，执行同步任务 |
| `goods_api_client.py` | API客户端和Excel管理器 |
| `config.py` | 配置文件 |
| `test_goods_api.py` | 测试脚本 |
| `demo.py` | 演示脚本 |
| `requirements.txt` | Python依赖 |
| `货品档案.xlsx` | Excel数据文件 |
| `goods_api.log` | 日志文件 |

## 功能特性

### ✅ 已实现功能
- 📊 Excel文件读取和写入
- 🔄 数据双向同步（API ↔ Excel）
- 📝 详细日志记录
- 🔁 自动重试机制
- 💾 自动备份Excel文件
- ⚙️ 灵活配置管理
- 🎯 支持指定货主范围
- 📈 数据统计和分析
- 🧪 完整的测试套件

### 🔧 API功能
- API签名生成和验证
- 错误处理和重试
- 网络超时处理
- 参数验证

### 📊 Excel功能
- 自动列映射
- 数据类型转换
- 增量更新
- 备份机制

## 数据字段映射

### API字段 → Excel字段
| API字段 | Excel字段 | 说明 |
|---------|-----------|------|
| owner_no | 货主 | 货主编号 |
| spec_no | 货品编号 | 商家编码 |
| goods_name | 货品名称 | 货品名称 |
| length | 长度(cm) | 长度 |
| width | 宽度(cm) | 宽度 |
| height | 高度(cm) | 高度 |
| volume | 体积(cm³) | 体积 |
| gross_weight | 毛重(kg) | 毛重 |
| base_unit | 基本单位 | 基本单位 |
| unit_ratio | 每箱数量 | 每箱数量 |
| box_weight | 箱重量(kg) | 箱重量 |
| box_volume | 箱体积(cm³) | 箱体积 |
| validity_days | 保质期天数 | 保质期天数 |
| expire_days | 临期天数 | 临期天数 |
| barcode | 条形码 | 条形码 |

## 常见问题

### Q: API返回"接口名字错误"
A: 这可能是因为：
1. API接口名称需要确认
2. 账号权限不足
3. API服务未部署该接口

建议联系API提供方确认正确的接口名称和权限。

### Q: Excel文件被占用
A: 确保Excel文件没有被其他程序打开，程序会自动备份原文件。

### Q: 网络连接问题
A: 检查网络连接和API服务器地址，程序有自动重试机制。

### Q: 数据同步不完整
A: 查看日志文件 `goods_api.log` 了解详细错误信息。

## 日志分析

日志文件包含以下信息：
- API请求和响应
- 数据处理过程
- 错误和警告
- 统计结果

示例日志：
```
2025-08-04 19:44:59,728 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 19:44:59,741 - INFO - 发送API请求: goods.query
2025-08-04 19:45:00,017 - INFO - API响应: failure
```

## 高级用法

### 自定义配置
修改 `config.py` 文件中的配置：
```python
WDT_CONFIG = {
    'SID': 'your_sid',
    'APP_KEY': 'your_app_key',
    'APP_SECRET': 'your_app_secret',
    'API_URL': 'your_api_url'
}
```

### 环境变量配置
```bash
export WDT_SID=your_sid
export WDT_APP_KEY=your_app_key
export WDT_APP_SECRET=your_app_secret
export WDT_API_URL=your_api_url
```

### 编程接口使用
```python
from goods_api_client import WDTApiClient, GoodsExcelManager

# 初始化
api_client = WDTApiClient()
excel_manager = GoodsExcelManager()

# 获取数据
result = api_client.get_goods_list('货主编号')

# 更新Excel
if result.get('flag') == 'success':
    goods_list = result.get('data', {}).get('goods', [])
    excel_manager.update_goods_data(goods_list)
    excel_manager.save_excel()
```

## 性能优化

### 批量处理
- 程序支持分页获取数据
- 自动处理大量数据
- 内存使用优化

### 网络优化
- 自动重试机制
- 请求间隔控制
- 超时处理

### 存储优化
- 增量更新
- 自动备份
- 压缩存储

## 安全注意事项

1. **API密钥安全**：不要在代码中硬编码密钥
2. **数据备份**：程序会自动备份，但建议定期手动备份
3. **权限控制**：确保API账号只有必要的权限
4. **日志安全**：日志文件可能包含敏感信息，注意保护

## 技术支持

如遇问题，请：
1. 查看日志文件 `goods_api.log`
2. 运行测试脚本 `python test_goods_api.py`
3. 检查网络连接和API权限
4. 联系技术支持

## 更新日志

### v1.0.0 (2025-08-04)
- 初始版本发布
- 支持API双向同步
- 完整的错误处理
- 自动备份机制
- 详细日志记录
