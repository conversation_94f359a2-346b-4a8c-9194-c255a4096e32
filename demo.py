#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
货品档案API同步工具演示脚本
"""

import sys
import pandas as pd
from goods_api_client import WDTApiClient, GoodsExcelManager
from goods_sync import GoodsSyncManager

def demo_excel_operations():
    """演示Excel操作"""
    print("=== Excel操作演示 ===")
    
    # 初始化Excel管理器
    excel_manager = GoodsExcelManager()
    
    print(f"当前Excel文件: {excel_manager.excel_path}")
    print(f"数据行数: {len(excel_manager.df)}")
    print(f"列数: {len(excel_manager.df.columns)}")
    
    # 显示货主统计
    if not excel_manager.df.empty:
        owners = excel_manager.df['货主'].value_counts()
        print("\n货主统计:")
        for owner, count in owners.head().items():
            print(f"  {owner}: {count}条")
    
    # 显示列信息
    print(f"\n列名: {list(excel_manager.df.columns)}")
    
    return excel_manager

def demo_add_sample_data():
    """演示添加示例数据"""
    print("\n=== 添加示例数据演示 ===")
    
    excel_manager = GoodsExcelManager()
    
    # 准备示例货品数据
    sample_goods = [
        {
            'owner_no': '测试货主',
            'spec_no': 'TEST001',
            'goods_name': '测试商品1',
            'length': '10.5',
            'width': '8.0',
            'height': '3.2',
            'volume': '268.8',
            'gross_weight': '0.5',
            'base_unit': '个',
            'unit_ratio': '12',
            'box_weight': '6.0',
            'box_volume': '3225.6',
            'validity_days': '365',
            'expire_days': '30'
        },
        {
            'owner_no': '测试货主',
            'spec_no': 'TEST002',
            'goods_name': '测试商品2',
            'length': '15.0',
            'width': '12.0',
            'height': '5.0',
            'volume': '900.0',
            'gross_weight': '1.2',
            'base_unit': '盒',
            'unit_ratio': '6',
            'box_weight': '7.2',
            'box_volume': '5400.0',
            'validity_days': '730',
            'expire_days': '60'
        }
    ]
    
    # 更新数据到Excel
    updated, new = excel_manager.update_goods_data(sample_goods)
    print(f"更新结果: 更新 {updated} 条, 新增 {new} 条")
    
    # 保存Excel文件
    excel_manager.save_excel()
    print("示例数据已添加并保存到Excel文件")
    
    return excel_manager

def demo_data_export():
    """演示数据导出"""
    print("\n=== 数据导出演示 ===")
    
    excel_manager = GoodsExcelManager()
    
    if excel_manager.df.empty:
        print("Excel文件为空，无数据可导出")
        return
    
    # 导出特定货主的数据
    test_owner = '测试货主'
    test_data = excel_manager.df[excel_manager.df['货主'] == test_owner]
    
    if not test_data.empty:
        export_file = f'{test_owner}_货品档案.xlsx'
        test_data.to_excel(export_file, index=False)
        print(f"已导出 {test_owner} 的 {len(test_data)} 条数据到 {export_file}")
    else:
        print(f"未找到货主 {test_owner} 的数据")
    
    # 导出包含新字段的数据
    new_fields = ['长度(cm)', '宽度(cm)', '高度(cm)', '体积(cm³)', '毛重(kg)']
    has_new_fields = excel_manager.df[new_fields].notna().any(axis=1)
    
    if has_new_fields.any():
        enhanced_data = excel_manager.df[has_new_fields]
        enhanced_file = '增强货品档案.xlsx'
        enhanced_data.to_excel(enhanced_file, index=False)
        print(f"已导出包含尺寸信息的 {len(enhanced_data)} 条数据到 {enhanced_file}")
    else:
        print("未找到包含尺寸信息的数据")

def demo_data_analysis():
    """演示数据分析"""
    print("\n=== 数据分析演示 ===")
    
    excel_manager = GoodsExcelManager()
    
    if excel_manager.df.empty:
        print("Excel文件为空，无法进行分析")
        return
    
    df = excel_manager.df
    
    # 基本统计
    print(f"总货品数量: {len(df)}")
    print(f"货主数量: {df['货主'].nunique()}")
    print(f"品牌数量: {df['品牌'].nunique()}")
    
    # 货主分布
    print("\n货主分布:")
    owner_stats = df['货主'].value_counts()
    for owner, count in owner_stats.head().items():
        percentage = (count / len(df)) * 100
        print(f"  {owner}: {count}条 ({percentage:.1f}%)")
    
    # 品牌分布
    print("\n品牌分布:")
    brand_stats = df['品牌'].value_counts()
    for brand, count in brand_stats.head().items():
        if pd.notna(brand):
            percentage = (count / len(df)) * 100
            print(f"  {brand}: {count}条 ({percentage:.1f}%)")
    
    # 检查新字段完整性
    new_fields = ['长度(cm)', '宽度(cm)', '高度(cm)', '体积(cm³)', '毛重(kg)']
    print("\n新字段数据完整性:")
    for field in new_fields:
        if field in df.columns:
            non_empty = df[field].notna().sum()
            percentage = (non_empty / len(df)) * 100
            print(f"  {field}: {non_empty}条 ({percentage:.1f}%)")

def demo_api_client():
    """演示API客户端（不实际调用API）"""
    print("\n=== API客户端演示 ===")
    
    # 初始化API客户端
    api_client = WDTApiClient()
    print(f"API客户端已初始化")
    print(f"SID: {api_client.sid}")
    print(f"API URL: {api_client.api_url}")
    
    # 演示签名生成
    test_params = {
        'owner_no': 'test',
        'spec_no': 'TEST001',
        'length': '10'
    }
    
    # 模拟公共参数
    common_params = {
        'sid': api_client.sid,
        'appkey': api_client.app_key,
        'timestamp': '1234567890',
        'method': 'goods.info.update',
        'format': 'json',
        'v': '1.0'
    }
    
    all_params = {**common_params, **test_params}
    sign = api_client._generate_sign(all_params)
    
    print(f"\n签名生成演示:")
    print(f"参数: {all_params}")
    print(f"生成的签名: {sign}")

def main():
    """主演示函数"""
    print("货品档案API同步工具演示")
    print("=" * 50)
    
    demos = [
        ("Excel操作演示", demo_excel_operations),
        ("添加示例数据", demo_add_sample_data),
        ("数据分析", demo_data_analysis),
        ("数据导出", demo_data_export),
        ("API客户端", demo_api_client),
    ]
    
    for demo_name, demo_func in demos:
        try:
            print(f"\n{'='*20} {demo_name} {'='*20}")
            demo_func()
        except Exception as e:
            print(f"演示 {demo_name} 时出错: {e}")
            continue
    
    print("\n" + "=" * 50)
    print("演示完成！")
    
    # 显示使用提示
    print("\n使用提示:")
    print("1. 运行 'python goods_sync.py --config-check' 检查配置")
    print("2. 运行 'python goods_sync.py' 从API同步数据到Excel")
    print("3. 运行 'python goods_sync.py --mode excel2api' 从Excel同步数据到API")
    print("4. 运行 'python test_goods_api.py' 进行系统测试")
    print("5. 查看 'goods_api.log' 了解详细日志")

if __name__ == '__main__':
    main()
