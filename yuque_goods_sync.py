#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语雀新接口货品同步工具
支持语雀(yuque.com)新接口的货品信息同步
"""

import sys
import argparse
import time
import pandas as pd
from typing import List, Dict, Any
import logging
from datetime import datetime, timedelta
from config import get_config
from yuque_api_client import YuqueApiClient, YuqueGoodsManager
from goods_api_client import GoodsExcelManager

# 配置日志
config = get_config()
logging.basicConfig(
    level=getattr(logging, config['log']['LEVEL']),
    format=config['log']['FORMAT'],
    handlers=[
        logging.FileHandler('yuque_goods_sync.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class YuqueGoodsSyncTool:
    """语雀货品同步工具"""
    
    def __init__(self, use_custom_api=True):
        """初始化同步工具"""
        self.config = get_config()
        self.yuque_client = YuqueApiClient(use_custom_api)
        self.excel_manager = GoodsExcelManager(self.config['excel']['FILE_PATH'])
        self.use_custom_api = use_custom_api
        
        logger.info(f"语雀货品同步工具初始化完成 - API类型: {'自定义' if use_custom_api else '标准语雀'}")
    
    def test_api_connection(self) -> bool:
        """测试API连接"""
        logger.info("测试语雀API连接...")
        
        try:
            result = self.yuque_client.test_connection()
            
            if self.use_custom_api:
                success = result.get('flag') == 'success'
            else:
                success = 'login' in result or 'id' in result
            
            if success:
                logger.info("✓ 语雀API连接成功")
                return True
            else:
                logger.error(f"✗ 语雀API连接失败: {result}")
                return False
                
        except Exception as e:
            logger.error(f"✗ 语雀API连接异常: {e}")
            return False
    
    def sync_goods_from_yuque(self, owner_list: List[str] = None, 
                             start_time: str = None, end_time: str = None) -> Dict[str, int]:
        """从语雀API同步货品数据到Excel"""
        logger.info("开始从语雀API同步货品数据")
        
        # 获取货主列表
        if not owner_list:
            if not self.excel_manager.df.empty:
                owner_list = self.excel_manager.df['货主'].dropna().unique().tolist()
            else:
                owner_list = ['北区京东POP店']  # 默认货主
        
        # 设置时间范围
        if not start_time or not end_time:
            end_dt = datetime.now()
            start_dt = end_dt - timedelta(hours=1)
            start_time = start_dt.strftime('%Y-%m-%d %H:%M:%S')
            end_time = end_dt.strftime('%Y-%m-%d %H:%M:%S')
        
        logger.info(f"同步参数 - 货主: {owner_list}, 时间: {start_time} 到 {end_time}")
        
        total_stats = {'updated': 0, 'new': 0, 'errors': 0}
        
        for owner in owner_list:
            if not owner or str(owner).strip() == '':
                continue
                
            logger.info(f"处理货主: {owner}")
            
            try:
                owner_stats = self._sync_single_owner(owner, start_time, end_time)
                for key in total_stats:
                    total_stats[key] += owner_stats.get(key, 0)
                    
                logger.info(f"货主 {owner} 处理完成 - "
                           f"更新: {owner_stats['updated']}条, "
                           f"新增: {owner_stats['new']}条")
                    
            except Exception as e:
                logger.error(f"处理货主 {owner} 时出错: {e}")
                total_stats['errors'] += 1
                continue
        
        # 保存Excel文件
        try:
            self.excel_manager.save_excel()
            logger.info("Excel文件保存成功")
        except Exception as e:
            logger.error(f"保存Excel文件失败: {e}")
            total_stats['errors'] += 1
        
        logger.info(f"语雀同步完成 - 总计更新: {total_stats['updated']}条, "
                   f"新增: {total_stats['new']}条, 错误: {total_stats['errors']}个")
        
        return total_stats
    
    def _sync_single_owner(self, owner: str, start_time: str, end_time: str) -> Dict[str, int]:
        """同步单个货主的数据"""
        stats = {'updated': 0, 'new': 0, 'errors': 0}
        page_no = 0
        page_size = 30
        
        while True:
            try:
                logger.debug(f"获取货主 {owner} 第 {page_no} 页数据")
                
                result = self.yuque_client.get_goods_list(
                    owner, start_time, end_time, page_no, page_size
                )
                
                if self.use_custom_api:
                    # 自定义API响应格式
                    if result.get('flag') != 'success':
                        error_msg = result.get('message', '未知错误')
                        logger.warning(f"获取货品列表失败: {error_msg}")
                        
                        if '接口' in error_msg and '名字错误' in error_msg:
                            logger.error(f"语雀API接口名称可能不正确")
                            logger.error(f"请确认语雀API接口是否正确部署")
                        
                        if page_no == 0:
                            stats['errors'] += 1
                        break
                    
                    content = result.get('content', [])
                    total_count = result.get('total', 0) if page_no == 0 else None
                    
                    if not content:
                        logger.debug(f"货主 {owner} 第 {page_no} 页无数据")
                        break
                    
                    # 更新Excel数据
                    updated, new = self.excel_manager.update_goods_data(content)
                    stats['updated'] += updated
                    stats['new'] += new
                    
                    logger.debug(f"货主 {owner} 第 {page_no} 页处理完成 - "
                               f"更新: {updated}条, 新增: {new}条")
                    
                    # 检查是否还有更多页
                    if total_count is not None:
                        logger.debug(f"货主 {owner} 总记录数: {total_count}")
                    
                    if len(content) < page_size:
                        logger.debug(f"货主 {owner} 数据获取完成")
                        break
                else:
                    # 标准语雀API响应格式
                    data = result.get('data', [])
                    
                    if not data:
                        logger.debug(f"货主 {owner} 第 {page_no} 页无数据")
                        break
                    
                    # 转换为标准格式
                    converted_data = self._convert_yuque_data(data)
                    
                    # 更新Excel数据
                    updated, new = self.excel_manager.update_goods_data(converted_data)
                    stats['updated'] += updated
                    stats['new'] += new
                    
                    if len(data) < page_size:
                        break
                
                page_no += 1
                time.sleep(0.5)  # 避免请求过快
                
            except Exception as e:
                logger.error(f"处理货主 {owner} 第 {page_no} 页时出错: {e}")
                stats['errors'] += 1
                break
        
        return stats
    
    def _convert_yuque_data(self, yuque_data: List[Dict]) -> List[Dict]:
        """转换标准语雀API数据格式为货品数据格式"""
        converted = []
        
        for item in yuque_data:
            # 这里需要根据实际的语雀API响应格式进行转换
            converted_item = {
                'spec_no': item.get('id', ''),
                'goods_name': item.get('title', ''),
                'goods_no': item.get('slug', ''),
                'spec_name': item.get('description', ''),
                # 其他字段根据实际API响应进行映射
            }
            converted.append(converted_item)
        
        return converted
    
    def get_sync_summary(self) -> Dict[str, Any]:
        """获取同步摘要"""
        if self.excel_manager.df.empty:
            return {
                'total_goods': 0,
                'owners': [],
                'has_api_data': 0,
                'api_service': 'Yuque'
            }
        
        df = self.excel_manager.df
        api_fields = ['长度(cm)', '宽度(cm)', '高度(cm)', '体积(ml)', '毛重(kg)']
        has_api_data = df[api_fields].notna().any(axis=1).sum()
        
        return {
            'total_goods': len(df),
            'owners': df['货主'].value_counts().to_dict(),
            'has_api_data': has_api_data,
            'api_service': 'Yuque'
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='语雀新接口货品同步工具')
    parser.add_argument('--owners', nargs='+', help='指定货主列表')
    parser.add_argument('--start-time', help='开始时间，格式：YYYY-MM-DD HH:MM:SS')
    parser.add_argument('--end-time', help='结束时间，格式：YYYY-MM-DD HH:MM:SS')
    parser.add_argument('--api-type', choices=['custom', 'standard'], default='custom',
                       help='API类型：custom=自定义API, standard=标准语雀API')
    parser.add_argument('--test-connection', action='store_true', help='测试API连接')
    parser.add_argument('--summary', action='store_true', help='显示数据摘要')
    
    args = parser.parse_args()
    
    try:
        use_custom_api = args.api_type == 'custom'
        sync_tool = YuqueGoodsSyncTool(use_custom_api)
        
        if args.test_connection:
            success = sync_tool.test_api_connection()
            if success:
                print("✓ 语雀API连接成功")
            else:
                print("✗ 语雀API连接失败，请检查配置")
            return
        
        if args.summary:
            summary = sync_tool.get_sync_summary()
            print("数据摘要:")
            print(f"  API服务: {summary['api_service']}")
            print(f"  总货品数量: {summary['total_goods']}")
            print(f"  包含API数据的货品: {summary['has_api_data']}")
            print("  货主分布:")
            for owner, count in summary['owners'].items():
                print(f"    {owner}: {count}条")
            return
        
        # 验证时间参数
        if args.start_time and args.end_time:
            try:
                start_dt = datetime.strptime(args.start_time, '%Y-%m-%d %H:%M:%S')
                end_dt = datetime.strptime(args.end_time, '%Y-%m-%d %H:%M:%S')
                
                time_diff = (end_dt - start_dt).total_seconds()
                if time_diff > 3600:
                    print("错误：时间跨度不能超过1小时")
                    return
                elif time_diff <= 0:
                    print("错误：结束时间必须大于开始时间")
                    return
                    
            except ValueError:
                print("错误：时间格式不正确，请使用 YYYY-MM-DD HH:MM:SS 格式")
                return
        
        # 执行同步
        logger.info("开始执行语雀API到Excel同步")
        stats = sync_tool.sync_goods_from_yuque(args.owners, args.start_time, args.end_time)
        
        print("语雀同步完成！")
        print(f"  更新: {stats['updated']}条")
        print(f"  新增: {stats['new']}条")
        print(f"  错误: {stats['errors']}个")
        
        if stats['errors'] > 0:
            print("请查看日志文件了解错误详情: yuque_goods_sync.log")
        
        logger.info("程序执行完成")
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        print("程序被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
