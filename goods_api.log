2025-08-04 19:44:59,728 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 19:44:59,740 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 19:44:59,741 - INFO - 发送API请求: goods.query
2025-08-04 19:45:00,017 - INFO - API响应: failure
2025-08-04 19:45:00,018 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 19:45:00,019 - INFO - 发送API请求: goods.info.update
2025-08-04 19:45:00,225 - INFO - API响应: failure
2025-08-04 19:46:08,078 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 19:46:08,192 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 19:46:08,198 - INFO - 数据更新完成 - 更新: 0条, 新增: 2条
2025-08-04 19:46:08,209 - INFO - 原文件已备份为: 货品档案.xlsx.backup_20250804_194608
2025-08-04 19:46:08,479 - INFO - Excel文件已保存: 货品档案.xlsx
2025-08-04 19:46:08,552 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1750行数据
2025-08-04 19:46:08,630 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1750行数据
2025-08-04 19:46:08,653 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 19:49:04,245 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1750行数据
2025-08-04 19:49:04,322 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1750行数据
2025-08-04 19:49:04,331 - INFO - 数据更新完成 - 更新: 2条, 新增: 0条
2025-08-04 19:49:04,337 - INFO - 原文件已备份为: 货品档案.xlsx.backup_20250804_194904
2025-08-04 19:49:04,586 - INFO - Excel文件已保存: 货品档案.xlsx
2025-08-04 19:49:04,667 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1750行数据
2025-08-04 19:49:04,749 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1750行数据
2025-08-04 19:49:04,773 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 19:58:51,065 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 19:58:51,296 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 19:58:51,296 - INFO - API到Excel同步器初始化完成
2025-08-04 19:58:53,442 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 19:58:53,663 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 19:58:53,663 - INFO - API到Excel同步器初始化完成
2025-08-04 20:00:36,411 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 20:00:36,624 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 20:00:36,624 - INFO - API到Excel同步器初始化完成
2025-08-04 20:00:36,624 - INFO - 开始执行API到Excel同步
2025-08-04 20:00:36,625 - INFO - 开始从API同步数据到Excel
2025-08-04 20:00:36,628 - INFO - 处理货主列表: ['北区京东POP店', '七彩虹新零售', '七彩虹天猫店', '七彩虹速卖通']
2025-08-04 20:00:36,628 - INFO - 开始处理货主: 北区京东POP店
2025-08-04 20:00:36,628 - INFO - 发送API请求: goods.query
2025-08-04 20:00:36,882 - INFO - API响应: failure
2025-08-04 20:00:36,883 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 20:00:36,883 - INFO - 货主 北区京东POP店 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 20:00:36,883 - INFO - 开始处理货主: 七彩虹新零售
2025-08-04 20:00:36,884 - INFO - 发送API请求: goods.query
2025-08-04 20:00:37,087 - INFO - API响应: failure
2025-08-04 20:00:37,087 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 20:00:37,087 - INFO - 货主 七彩虹新零售 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 20:00:37,088 - INFO - 开始处理货主: 七彩虹天猫店
2025-08-04 20:00:37,088 - INFO - 发送API请求: goods.query
2025-08-04 20:00:37,281 - INFO - API响应: failure
2025-08-04 20:00:37,282 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 20:00:37,282 - INFO - 货主 七彩虹天猫店 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 20:00:37,282 - INFO - 开始处理货主: 七彩虹速卖通
2025-08-04 20:00:37,282 - INFO - 发送API请求: goods.query
2025-08-04 20:00:37,474 - INFO - API响应: failure
2025-08-04 20:00:37,475 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 20:00:37,476 - INFO - 货主 七彩虹速卖通 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 20:00:37,485 - INFO - 原文件已备份为: 货品档案.xlsx.backup_20250804_200037
2025-08-04 20:00:37,739 - INFO - Excel文件已保存: 货品档案.xlsx
2025-08-04 20:00:37,739 - INFO - Excel文件保存成功
2025-08-04 20:00:37,739 - INFO - 同步完成 - 总计更新: 0条, 新增: 0条, 错误: 4个
2025-08-04 20:00:37,740 - INFO - 程序执行完成
