2025-08-04 19:44:59,728 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 19:44:59,740 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 19:44:59,741 - INFO - 发送API请求: goods.query
2025-08-04 19:45:00,017 - INFO - API响应: failure
2025-08-04 19:45:00,018 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 19:45:00,019 - INFO - 发送API请求: goods.info.update
2025-08-04 19:45:00,225 - INFO - API响应: failure
2025-08-04 19:46:08,078 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 19:46:08,192 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 19:46:08,198 - INFO - 数据更新完成 - 更新: 0条, 新增: 2条
2025-08-04 19:46:08,209 - INFO - 原文件已备份为: 货品档案.xlsx.backup_20250804_194608
2025-08-04 19:46:08,479 - INFO - Excel文件已保存: 货品档案.xlsx
2025-08-04 19:46:08,552 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1750行数据
2025-08-04 19:46:08,630 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1750行数据
2025-08-04 19:46:08,653 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 19:49:04,245 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1750行数据
2025-08-04 19:49:04,322 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1750行数据
2025-08-04 19:49:04,331 - INFO - 数据更新完成 - 更新: 2条, 新增: 0条
2025-08-04 19:49:04,337 - INFO - 原文件已备份为: 货品档案.xlsx.backup_20250804_194904
2025-08-04 19:49:04,586 - INFO - Excel文件已保存: 货品档案.xlsx
2025-08-04 19:49:04,667 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1750行数据
2025-08-04 19:49:04,749 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1750行数据
2025-08-04 19:49:04,773 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 19:58:51,065 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 19:58:51,296 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 19:58:51,296 - INFO - API到Excel同步器初始化完成
2025-08-04 19:58:53,442 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 19:58:53,663 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 19:58:53,663 - INFO - API到Excel同步器初始化完成
2025-08-04 20:00:36,411 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 20:00:36,624 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 20:00:36,624 - INFO - API到Excel同步器初始化完成
2025-08-04 20:00:36,624 - INFO - 开始执行API到Excel同步
2025-08-04 20:00:36,625 - INFO - 开始从API同步数据到Excel
2025-08-04 20:00:36,628 - INFO - 处理货主列表: ['北区京东POP店', '七彩虹新零售', '七彩虹天猫店', '七彩虹速卖通']
2025-08-04 20:00:36,628 - INFO - 开始处理货主: 北区京东POP店
2025-08-04 20:00:36,628 - INFO - 发送API请求: goods.query
2025-08-04 20:00:36,882 - INFO - API响应: failure
2025-08-04 20:00:36,883 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 20:00:36,883 - INFO - 货主 北区京东POP店 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 20:00:36,883 - INFO - 开始处理货主: 七彩虹新零售
2025-08-04 20:00:36,884 - INFO - 发送API请求: goods.query
2025-08-04 20:00:37,087 - INFO - API响应: failure
2025-08-04 20:00:37,087 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 20:00:37,087 - INFO - 货主 七彩虹新零售 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 20:00:37,088 - INFO - 开始处理货主: 七彩虹天猫店
2025-08-04 20:00:37,088 - INFO - 发送API请求: goods.query
2025-08-04 20:00:37,281 - INFO - API响应: failure
2025-08-04 20:00:37,282 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 20:00:37,282 - INFO - 货主 七彩虹天猫店 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 20:00:37,282 - INFO - 开始处理货主: 七彩虹速卖通
2025-08-04 20:00:37,282 - INFO - 发送API请求: goods.query
2025-08-04 20:00:37,474 - INFO - API响应: failure
2025-08-04 20:00:37,475 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 20:00:37,476 - INFO - 货主 七彩虹速卖通 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 20:00:37,485 - INFO - 原文件已备份为: 货品档案.xlsx.backup_20250804_200037
2025-08-04 20:00:37,739 - INFO - Excel文件已保存: 货品档案.xlsx
2025-08-04 20:00:37,739 - INFO - Excel文件保存成功
2025-08-04 20:00:37,739 - INFO - 同步完成 - 总计更新: 0条, 新增: 0条, 错误: 4个
2025-08-04 20:00:37,740 - INFO - 程序执行完成
2025-08-04 20:03:24,827 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 20:03:24,828 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 20:03:25,044 - INFO - API响应: failure
2025-08-04 20:03:25,046 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 20:03:25,046 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 20:03:25,240 - INFO - API响应: failure
2025-08-04 20:03:25,241 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 20:03:25,442 - INFO - API响应: failure
2025-08-04 20:03:25,443 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 20:03:25,646 - INFO - API响应: failure
2025-08-04 20:03:40,808 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 20:03:40,808 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 20:03:41,000 - INFO - API响应: failure
2025-08-04 20:11:54,214 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 20:11:54,215 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 20:11:54,424 - INFO - API响应: failure
2025-08-04 20:11:54,425 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 20:11:54,426 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 20:11:54,625 - INFO - API响应: failure
2025-08-04 20:15:19,140 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 20:15:19,360 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 20:15:19,361 - INFO - API到Excel同步器初始化完成
2025-08-04 20:15:19,361 - INFO - 开始执行API到Excel同步
2025-08-04 20:15:19,361 - INFO - 开始从API同步数据到Excel
2025-08-04 20:15:19,361 - INFO - 处理货主列表: ['北区京东POP店', '七彩虹新零售', '七彩虹天猫店', '七彩虹速卖通']
2025-08-04 20:15:19,361 - INFO - 开始处理货主: 北区京东POP店
2025-08-04 20:15:19,362 - INFO - 同步时间范围: 2025-08-04 19:15:19 到 2025-08-04 20:15:19
2025-08-04 20:15:19,362 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 20:15:19,580 - INFO - API响应: failure
2025-08-04 20:15:19,580 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 20:15:19,580 - ERROR - API接口名称可能不正确，当前使用: goods.spec.query.step
2025-08-04 20:15:19,580 - ERROR - 请联系API提供方确认正确的接口名称
2025-08-04 20:15:19,581 - INFO - 货主 北区京东POP店 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 20:15:19,581 - INFO - 开始处理货主: 七彩虹新零售
2025-08-04 20:15:19,581 - INFO - 同步时间范围: 2025-08-04 19:15:19 到 2025-08-04 20:15:19
2025-08-04 20:15:19,581 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 20:15:19,788 - INFO - API响应: failure
2025-08-04 20:15:19,789 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 20:15:19,789 - ERROR - API接口名称可能不正确，当前使用: goods.spec.query.step
2025-08-04 20:15:19,789 - ERROR - 请联系API提供方确认正确的接口名称
2025-08-04 20:15:19,789 - INFO - 货主 七彩虹新零售 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 20:15:19,789 - INFO - 开始处理货主: 七彩虹天猫店
2025-08-04 20:15:19,789 - INFO - 同步时间范围: 2025-08-04 19:15:19 到 2025-08-04 20:15:19
2025-08-04 20:15:19,789 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 20:15:19,970 - INFO - API响应: failure
2025-08-04 20:15:19,970 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 20:15:19,971 - ERROR - API接口名称可能不正确，当前使用: goods.spec.query.step
2025-08-04 20:15:19,971 - ERROR - 请联系API提供方确认正确的接口名称
2025-08-04 20:15:19,971 - INFO - 货主 七彩虹天猫店 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 20:15:19,972 - INFO - 开始处理货主: 七彩虹速卖通
2025-08-04 20:15:19,972 - INFO - 同步时间范围: 2025-08-04 19:15:19 到 2025-08-04 20:15:19
2025-08-04 20:15:19,972 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 20:15:20,171 - INFO - API响应: failure
2025-08-04 20:15:20,171 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 20:15:20,172 - ERROR - API接口名称可能不正确，当前使用: goods.spec.query.step
2025-08-04 20:15:20,172 - ERROR - 请联系API提供方确认正确的接口名称
2025-08-04 20:15:20,173 - INFO - 货主 七彩虹速卖通 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 20:15:20,184 - INFO - 原文件已备份为: 货品档案.xlsx.backup_20250804_201520
2025-08-04 20:15:20,447 - INFO - Excel文件已保存: 货品档案.xlsx
2025-08-04 20:15:20,447 - INFO - Excel文件保存成功
2025-08-04 20:15:20,447 - INFO - 同步完成 - 总计更新: 0条, 新增: 0条, 错误: 0个
2025-08-04 20:15:20,447 - INFO - 程序执行完成
2025-08-04 20:24:08,497 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 20:24:08,728 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 20:24:08,728 - INFO - API到Excel同步器初始化完成
2025-08-04 20:24:08,728 - INFO - 开始执行API到Excel同步
2025-08-04 20:24:08,729 - INFO - 开始从API同步数据到Excel
2025-08-04 20:24:08,729 - INFO - 处理货主列表: ['北区京东POP店', '七彩虹新零售', '七彩虹天猫店', '七彩虹速卖通']
2025-08-04 20:24:08,729 - INFO - 开始处理货主: 北区京东POP店
2025-08-04 20:24:08,729 - INFO - 同步时间范围: 2025-08-04 19:24:08 到 2025-08-04 20:24:08
2025-08-04 20:24:08,729 - INFO - 使用API接口: goods.spec.query.step
2025-08-04 20:24:08,730 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 20:24:09,011 - INFO - API响应: failure
2025-08-04 20:24:09,011 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 20:24:09,011 - ERROR - API接口名称可能不正确，当前使用: goods.spec.query.step
2025-08-04 20:24:09,011 - ERROR - 请联系API提供方确认正确的接口名称
2025-08-04 20:24:09,011 - INFO - 货主 北区京东POP店 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 20:24:09,012 - INFO - 开始处理货主: 七彩虹新零售
2025-08-04 20:24:09,012 - INFO - 同步时间范围: 2025-08-04 19:24:09 到 2025-08-04 20:24:09
2025-08-04 20:24:09,012 - INFO - 使用API接口: goods.spec.query.step
2025-08-04 20:24:09,012 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 20:24:09,224 - INFO - API响应: failure
2025-08-04 20:24:09,224 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 20:24:09,225 - ERROR - API接口名称可能不正确，当前使用: goods.spec.query.step
2025-08-04 20:24:09,225 - ERROR - 请联系API提供方确认正确的接口名称
2025-08-04 20:24:09,225 - INFO - 货主 七彩虹新零售 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 20:24:09,225 - INFO - 开始处理货主: 七彩虹天猫店
2025-08-04 20:24:09,225 - INFO - 同步时间范围: 2025-08-04 19:24:09 到 2025-08-04 20:24:09
2025-08-04 20:24:09,225 - INFO - 使用API接口: goods.spec.query.step
2025-08-04 20:24:09,225 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 20:24:09,440 - INFO - API响应: failure
2025-08-04 20:24:09,441 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 20:24:09,441 - ERROR - API接口名称可能不正确，当前使用: goods.spec.query.step
2025-08-04 20:24:09,442 - ERROR - 请联系API提供方确认正确的接口名称
2025-08-04 20:24:09,442 - INFO - 货主 七彩虹天猫店 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 20:24:09,443 - INFO - 开始处理货主: 七彩虹速卖通
2025-08-04 20:24:09,443 - INFO - 同步时间范围: 2025-08-04 19:24:09 到 2025-08-04 20:24:09
2025-08-04 20:24:09,443 - INFO - 使用API接口: goods.spec.query.step
2025-08-04 20:24:09,444 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 20:24:09,630 - INFO - API响应: failure
2025-08-04 20:24:09,631 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 20:24:09,631 - ERROR - API接口名称可能不正确，当前使用: goods.spec.query.step
2025-08-04 20:24:09,631 - ERROR - 请联系API提供方确认正确的接口名称
2025-08-04 20:24:09,632 - INFO - 货主 七彩虹速卖通 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 20:24:09,639 - INFO - 原文件已备份为: 货品档案.xlsx.backup_20250804_202409
2025-08-04 20:24:09,900 - INFO - Excel文件已保存: 货品档案.xlsx
2025-08-04 20:24:09,900 - INFO - Excel文件保存成功
2025-08-04 20:24:09,900 - INFO - 同步完成 - 总计更新: 0条, 新增: 0条, 错误: 0个
2025-08-04 20:24:09,900 - INFO - 程序执行完成
2025-08-04 21:08:01,641 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 21:08:01,866 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 21:08:01,866 - INFO - API到Excel同步器初始化完成
2025-08-04 21:08:01,866 - INFO - 开始执行API到Excel同步
2025-08-04 21:08:01,867 - INFO - 开始从API同步数据到Excel
2025-08-04 21:08:01,867 - INFO - 处理货主列表: ['北区京东POP店', '七彩虹新零售', '七彩虹天猫店', '七彩虹速卖通']
2025-08-04 21:08:01,867 - INFO - 开始处理货主: 北区京东POP店
2025-08-04 21:08:01,867 - INFO - 同步时间范围: 2025-08-04 20:08:01 到 2025-08-04 21:08:01
2025-08-04 21:08:01,867 - INFO - 使用API接口: goods.spec.query.step
2025-08-04 21:08:01,868 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 21:08:02,148 - INFO - API响应: failure
2025-08-04 21:08:02,149 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 21:08:02,149 - ERROR - API接口名称可能不正确，当前使用: goods.spec.query.step
2025-08-04 21:08:02,150 - ERROR - 请联系API提供方确认正确的接口名称
2025-08-04 21:08:02,150 - INFO - 货主 北区京东POP店 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 21:08:02,150 - INFO - 开始处理货主: 七彩虹新零售
2025-08-04 21:08:02,151 - INFO - 同步时间范围: 2025-08-04 20:08:02 到 2025-08-04 21:08:02
2025-08-04 21:08:02,151 - INFO - 使用API接口: goods.spec.query.step
2025-08-04 21:08:02,151 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 21:08:02,341 - INFO - API响应: failure
2025-08-04 21:08:02,341 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 21:08:02,341 - ERROR - API接口名称可能不正确，当前使用: goods.spec.query.step
2025-08-04 21:08:02,342 - ERROR - 请联系API提供方确认正确的接口名称
2025-08-04 21:08:02,342 - INFO - 货主 七彩虹新零售 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 21:08:02,342 - INFO - 开始处理货主: 七彩虹天猫店
2025-08-04 21:08:02,342 - INFO - 同步时间范围: 2025-08-04 20:08:02 到 2025-08-04 21:08:02
2025-08-04 21:08:02,342 - INFO - 使用API接口: goods.spec.query.step
2025-08-04 21:08:02,342 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 21:08:02,519 - INFO - API响应: failure
2025-08-04 21:08:02,520 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 21:08:02,520 - ERROR - API接口名称可能不正确，当前使用: goods.spec.query.step
2025-08-04 21:08:02,520 - ERROR - 请联系API提供方确认正确的接口名称
2025-08-04 21:08:02,520 - INFO - 货主 七彩虹天猫店 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 21:08:02,521 - INFO - 开始处理货主: 七彩虹速卖通
2025-08-04 21:08:02,521 - INFO - 同步时间范围: 2025-08-04 20:08:02 到 2025-08-04 21:08:02
2025-08-04 21:08:02,521 - INFO - 使用API接口: goods.spec.query.step
2025-08-04 21:08:02,521 - INFO - 发送API请求: goods.spec.query.step
2025-08-04 21:08:02,709 - INFO - API响应: failure
2025-08-04 21:08:02,710 - WARNING - 获取货品列表失败: 接口【】名字错误
2025-08-04 21:08:02,710 - ERROR - API接口名称可能不正确，当前使用: goods.spec.query.step
2025-08-04 21:08:02,711 - ERROR - 请联系API提供方确认正确的接口名称
2025-08-04 21:08:02,711 - INFO - 货主 七彩虹速卖通 处理完成 - 更新: 0条, 新增: 0条
2025-08-04 21:08:02,720 - INFO - 原文件已备份为: 货品档案.xlsx.backup_20250804_210802
2025-08-04 21:08:02,978 - INFO - Excel文件已保存: 货品档案.xlsx
2025-08-04 21:08:02,979 - INFO - Excel文件保存成功
2025-08-04 21:08:02,979 - INFO - 同步完成 - 总计更新: 0条, 新增: 0条, 错误: 0个
2025-08-04 21:08:02,979 - INFO - 程序执行完成
