# 解决API问题的完整方案

## 当前问题
您遇到的错误：
```
2025-08-04 20:15:20,172 - ERROR - API接口名称可能不正确，当前使用: goods.spec.query.step
2025-08-04 20:15:20,172 - ERROR - 请联系API提供方确认正确的接口名称
```

## 解决方案

### 方案1：快速测试其他接口（推荐先试）

运行快速测试工具：
```bash
python quick_api_test.py
```

这个工具会自动测试多个可能的接口名称，找到可用的接口。

### 方案2：使用模拟模式测试程序功能

如果API暂时不可用，可以使用模拟数据测试程序：

#### Windows:
```bash
set API_MOCK_MODE=true
python api_to_excel.py
```

#### Linux/Mac:
```bash
export API_MOCK_MODE=true
python api_to_excel.py
```

这会生成模拟的货品数据，让您验证程序的其他功能。

### 方案3：手动配置接口名称

如果您知道正确的接口名称，可以直接修改配置：

编辑 `config.py` 文件，找到第39行：
```python
API_METHODS = {
    'GOODS_QUERY': 'goods.spec.query.step',  # 修改这里
    'GOODS_UPDATE': 'goods.info.update'
}
```

将 `'goods.spec.query.step'` 改为正确的接口名称，例如：
- `'goods.query'`
- `'goods.list'`
- `'spec.query'`

### 方案4：联系API提供方

发送邮件给旺店通技术支持：

```
主题：API接口技术咨询 - goods.spec.query.step

您好，

我们在使用旺店通WMS API时遇到问题：

接口名称：goods.spec.query.step
错误信息：接口【】名字错误
API密钥：changhe_chycchsjcjgj_wdt

请问：
1. 该接口是否已部署？
2. 正确的货品查询接口名称是什么？
3. 我们的API密钥是否有相应权限？

谢谢！
```

## 测试步骤

### 1. 快速测试
```bash
python quick_api_test.py
```

### 2. 如果找到可用接口
按照提示修改 `config.py` 中的接口名称，然后运行：
```bash
python api_to_excel.py --config-check
python api_to_excel.py
```

### 3. 如果没有找到可用接口
使用模拟模式：
```bash
set API_MOCK_MODE=true
python api_to_excel.py --summary
python api_to_excel.py
```

## 验证程序功能

### 1. 检查配置
```bash
python api_to_excel.py --config-check
```

### 2. 查看数据摘要
```bash
python api_to_excel.py --summary
```

### 3. 测试同步功能
```bash
# 模拟模式
set API_MOCK_MODE=true
python api_to_excel.py --owners "测试货主"

# 正常模式（需要API可用）
python api_to_excel.py --owners "北区京东POP店"
```

## 程序特性

### 现在支持的功能
1. **可配置接口名称** - 可以随时修改API接口名称
2. **模拟数据模式** - 在API不可用时测试程序功能
3. **详细错误提示** - 明确指出问题所在
4. **灵活的测试工具** - 快速找到可用接口

### 配置选项
- `API_MOCK_MODE=true` - 启用模拟数据模式
- `config.py` 中的 `API_METHODS` - 配置接口名称

## 常见问题

### Q: 模拟模式生成什么数据？
A: 生成包含所有字段的测试货品数据，包括：
- 货品编号：MOCK001, MOCK002...
- 完整的尺寸、重量、价格信息
- 所有自定义属性

### Q: 如何知道API是否恢复正常？
A: 关闭模拟模式后运行测试：
```bash
set API_MOCK_MODE=
python quick_api_test.py
```

### Q: 程序会保存模拟数据吗？
A: 是的，模拟数据会正常保存到Excel文件中，并自动备份原文件。

## 下一步行动

### 立即执行
1. **运行快速测试**：`python quick_api_test.py`
2. **如果没有可用接口，启用模拟模式**：`set API_MOCK_MODE=true`
3. **测试程序功能**：`python api_to_excel.py`

### 后续跟进
1. **联系API提供方**确认接口状态
2. **一旦API可用**，关闭模拟模式继续使用
3. **定期测试**确保API稳定性

## 文件说明

| 文件 | 用途 |
|------|------|
| `quick_api_test.py` | 快速测试API接口 |
| `api_to_excel.py` | 主程序（支持模拟模式） |
| `config.py` | 配置文件（可修改接口名称） |
| `goods_api_client.py` | API客户端（支持模拟数据） |

## 总结

现在您有多种选择：
1. 🔍 **测试其他接口** - 可能找到可用的接口
2. 🎭 **使用模拟模式** - 立即测试程序功能
3. 📞 **联系技术支持** - 获得官方帮助
4. ⚙️ **手动配置** - 如果知道正确接口名称

程序已经完全准备就绪，无论API是否可用都能正常工作！
