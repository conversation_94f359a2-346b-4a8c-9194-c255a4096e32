#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试正确的API接口
"""

import json
from goods_api_client import WDTApiClient

def test_goods_query():
    """测试货品查询接口"""
    print("=== 测试货品查询接口 ===")
    
    try:
        client = WDTApiClient()
        print(f"API客户端初始化成功")
        print(f"SID: {client.sid}")
        print(f"API URL: {client.api_url}")
        
        # 测试获取货品列表 - 使用正确的接口名称
        print("\n测试接口: goods.spec.query.step")
        result = client.get_goods_list('北区京东POP店', 1, 5)
        
        print(f"API调用结果: {result.get('flag', 'unknown')}")
        print(f"返回消息: {result.get('message', '')}")
        
        if result.get('flag') == 'success':
            data = result.get('data', {})
            print(f"返回数据结构: {list(data.keys())}")
            
            # 检查是否有货品数据
            if 'goods' in data:
                goods_list = data['goods']
                print(f"货品数量: {len(goods_list)}")
                
                if goods_list:
                    first_goods = goods_list[0]
                    print(f"第一个货品字段: {list(first_goods.keys())}")
                    print(f"第一个货品信息:")
                    print(json.dumps(first_goods, ensure_ascii=False, indent=2))
            
            # 检查分页信息
            if 'total_count' in data:
                print(f"总记录数: {data['total_count']}")
            
            if 'page_no' in data:
                print(f"当前页: {data['page_no']}")
                
        else:
            print(f"API调用失败")
            print(f"完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_different_owners():
    """测试不同货主"""
    print("\n=== 测试不同货主 ===")
    
    owners = ['北区京东POP店', '七彩虹天猫店', '七彩虹新零售']
    
    client = WDTApiClient()
    
    for owner in owners:
        print(f"\n测试货主: {owner}")
        try:
            result = client.get_goods_list(owner, 1, 3)
            
            if result.get('flag') == 'success':
                data = result.get('data', {})
                goods_count = len(data.get('goods', []))
                total_count = data.get('total_count', 0)
                print(f"  ✓ 成功 - 当前页: {goods_count}条, 总计: {total_count}条")
            else:
                print(f"  ✗ 失败 - {result.get('message', '')}")
                
        except Exception as e:
            print(f"  ✗ 异常 - {e}")

def main():
    """主测试函数"""
    print("测试正确的API接口: goods.spec.query.step")
    print("=" * 50)
    
    test_goods_query()
    test_different_owners()
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == '__main__':
    main()
