#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语雀(Yuque)新接口API客户端
"""

import os
import json
import hashlib
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging
from yuque_api_config import get_yuque_config

logger = logging.getLogger(__name__)


class YuqueApiClient:
    """语雀API客户端"""
    
    def __init__(self, use_custom_api=True):
        """初始化API客户端
        
        Args:
            use_custom_api: True使用自定义API，False使用标准语雀API
        """
        self.config = get_yuque_config()
        self.use_custom_api = use_custom_api
        
        if use_custom_api:
            # 使用自定义的yuque.com API（类似旺店通格式）
            self.api_config = self.config['custom_yuque']
            self.sid = self.api_config['SID']
            self.app_key = self.api_config['APP_KEY']
            self.app_secret = self.api_config['APP_SECRET']
            self.api_url = self.api_config['API_URL']
            logger.info(f"使用自定义Yuque API - URL: {self.api_url}")
        else:
            # 使用标准语雀API
            self.api_config = self.config['yuque']
            self.token = self.api_config['TOKEN']
            self.api_url = self.api_config['API_URL']
            self.namespace = self.api_config['NAMESPACE']
            logger.info(f"使用标准Yuque API - URL: {self.api_url}")
    
    def _generate_sign(self, params: Dict[str, Any]) -> str:
        """生成API签名（用于自定义API）"""
        if not self.use_custom_api:
            return ""
        
        # 按key排序参数
        sorted_params = sorted(params.items())
        
        # 拼接参数字符串
        param_str = ''.join([f"{k}{v}" for k, v in sorted_params])
        
        # 加上app_secret
        sign_str = self.app_secret + param_str + self.app_secret
        
        # MD5加密并转大写
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
    
    def _make_request(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """发送API请求"""
        if self.use_custom_api:
            return self._make_custom_request(method, params)
        else:
            return self._make_yuque_request(method, params)
    
    def _make_custom_request(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """发送自定义API请求（类似旺店通格式）"""
        # 公共参数
        common_params = {
            'sid': self.sid,
            'appkey': self.app_key,
            'timestamp': str(int(time.time())),
            'method': method,
            'format': 'json',
            'v': '1.0'
        }
        
        # 合并参数
        all_params = {**common_params, **params}
        
        # 生成签名
        all_params['sign'] = self._generate_sign(all_params)
        
        try:
            logger.info(f"发送Yuque API请求: {method}")
            response = requests.post(self.api_url, data=all_params, 
                                   timeout=self.api_config['TIMEOUT'])
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"Yuque API响应: {result.get('flag', 'unknown')}")
            
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Yuque API请求失败: {e}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            raise
    
    def _make_yuque_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """发送标准语雀API请求"""
        if not self.token:
            raise ValueError("语雀Token未配置")
        
        headers = {
            'X-Auth-Token': self.token,
            'Content-Type': 'application/json',
            'User-Agent': 'Goods-Archive-Sync/1.0'
        }
        
        url = f"{self.api_url}/{endpoint}"
        
        try:
            logger.info(f"发送语雀API请求: {endpoint}")
            
            if params:
                response = requests.post(url, json=params, headers=headers,
                                       timeout=self.api_config['TIMEOUT'])
            else:
                response = requests.get(url, headers=headers,
                                      timeout=self.api_config['TIMEOUT'])
            
            response.raise_for_status()
            result = response.json()
            
            logger.info(f"语雀API响应成功")
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"语雀API请求失败: {e}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            raise
    
    def get_goods_list(self, owner_no: str, start_time: str, end_time: str, 
                      page_no: int = 0, page_size: int = 30) -> Dict[str, Any]:
        """获取货品列表"""
        if self.use_custom_api:
            # 使用自定义API格式
            params = {
                'owner_no': owner_no,
                'start_time': start_time,
                'end_time': end_time,
                'page_no': str(page_no),
                'page_size': str(page_size)
            }
            
            method_name = self.config['api_methods'].get('GOODS_QUERY', 'goods.spec.query.step')
            return self._make_request(method_name, params)
        else:
            # 使用标准语雀API格式
            params = {
                'owner_no': owner_no,
                'start_time': start_time,
                'end_time': end_time,
                'page': page_no,
                'limit': page_size
            }
            
            return self._make_request('goods/list', params)
    
    def update_goods_info(self, goods_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新货品信息"""
        if self.use_custom_api:
            method_name = self.config['api_methods'].get('GOODS_UPDATE', 'goods.info.update')
            return self._make_request(method_name, goods_data)
        else:
            return self._make_request('goods/update', goods_data)
    
    def test_connection(self) -> Dict[str, Any]:
        """测试API连接"""
        try:
            if self.use_custom_api:
                # 测试自定义API
                params = {'test': 'connection'}
                return self._make_request('system.ping', params)
            else:
                # 测试标准语雀API
                return self._make_request('user', {})
        except Exception as e:
            return {
                'flag': 'failure',
                'message': str(e)
            }


class YuqueGoodsManager:
    """语雀货品管理器"""
    
    def __init__(self, use_custom_api=True):
        """初始化管理器"""
        self.api_client = YuqueApiClient(use_custom_api)
        self.use_custom_api = use_custom_api
    
    def sync_goods_data(self, owner_list: List[str], start_time: str = None, 
                       end_time: str = None) -> Dict[str, int]:
        """同步货品数据"""
        from datetime import datetime, timedelta
        
        stats = {'updated': 0, 'new': 0, 'errors': 0}
        
        # 如果没有指定时间范围，使用最近1小时
        if not start_time or not end_time:
            end_dt = datetime.now()
            start_dt = end_dt - timedelta(hours=1)
            start_time = start_dt.strftime('%Y-%m-%d %H:%M:%S')
            end_time = end_dt.strftime('%Y-%m-%d %H:%M:%S')
        
        logger.info(f"开始同步货品数据 - 时间范围: {start_time} 到 {end_time}")
        
        for owner in owner_list:
            try:
                logger.info(f"处理货主: {owner}")
                
                page_no = 0
                while True:
                    result = self.api_client.get_goods_list(
                        owner, start_time, end_time, page_no, 30
                    )
                    
                    if self.use_custom_api:
                        # 自定义API响应格式
                        if result.get('flag') != 'success':
                            logger.warning(f"获取数据失败: {result.get('message', '')}")
                            break
                        
                        content = result.get('content', [])
                        if not content:
                            break
                        
                        stats['new'] += len(content)
                        logger.info(f"获取到 {len(content)} 条数据")
                        
                        # 检查是否还有更多页
                        if len(content) < 30:
                            break
                    else:
                        # 标准语雀API响应格式
                        data = result.get('data', [])
                        if not data:
                            break
                        
                        stats['new'] += len(data)
                        logger.info(f"获取到 {len(data)} 条数据")
                        
                        # 检查分页
                        if len(data) < 30:
                            break
                    
                    page_no += 1
                    
            except Exception as e:
                logger.error(f"处理货主 {owner} 时出错: {e}")
                stats['errors'] += 1
        
        return stats


def main():
    """测试函数"""
    print("语雀API客户端测试")
    print("=" * 50)
    
    # 测试自定义API
    print("1. 测试自定义API")
    custom_client = YuqueApiClient(use_custom_api=True)
    result = custom_client.test_connection()
    print(f"自定义API测试: {result.get('flag', 'unknown')}")
    
    # 测试标准语雀API
    print("\n2. 测试标准语雀API")
    yuque_client = YuqueApiClient(use_custom_api=False)
    result = yuque_client.test_connection()
    print(f"标准语雀API测试: {result}")
    
    # 测试货品数据同步
    print("\n3. 测试货品数据同步")
    manager = YuqueGoodsManager(use_custom_api=True)
    stats = manager.sync_goods_data(['北区京东POP店'])
    print(f"同步结果: {stats}")


if __name__ == '__main__':
    main()
