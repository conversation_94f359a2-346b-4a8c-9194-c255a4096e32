#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
签名算法测试工具 - 测试不同的签名生成方式
"""

import hashlib
import time
import requests
import urllib.parse

class SignatureTest:
    """签名算法测试"""
    
    def __init__(self):
        self.SID = 'changhe'
        self.APP_KEY = 'changhe_chycchsjcjgj_wdt'
        self.APP_SECRET = '72a70a3bedc51497a2a3b9c229d7df69'
        self.API_URL = 'https://openapi.wdtwms.com/open_api/service.php'
    
    def test_signature_algorithms(self):
        """测试不同的签名算法"""
        print("测试不同的签名算法")
        print("=" * 50)
        
        # 基础参数
        base_params = {
            'sid': self.SID,
            'appkey': self.APP_KEY,
            'timestamp': str(int(time.time())),
            'method': 'goods.spec.query.step',
            'format': 'json',
            'v': '1.0',
            'owner_no': 'test'
        }
        
        # 测试不同的签名算法
        algorithms = [
            {
                'name': '当前算法：secret + params + secret',
                'func': self.sign_method_1
            },
            {
                'name': '算法2：params + secret',
                'func': self.sign_method_2
            },
            {
                'name': '算法3：secret + params',
                'func': self.sign_method_3
            },
            {
                'name': '算法4：参数用&连接 + secret',
                'func': self.sign_method_4
            },
            {
                'name': '算法5：小写MD5',
                'func': self.sign_method_5
            },
            {
                'name': '算法6：参数=值&格式',
                'func': self.sign_method_6
            }
        ]
        
        for algo in algorithms:
            print(f"\n{algo['name']}:")
            print("-" * 30)
            
            try:
                # 生成签名
                params = base_params.copy()
                sign = algo['func'](params)
                params['sign'] = sign
                
                print(f"生成的签名: {sign}")
                
                # 测试API调用
                response = requests.post(self.API_URL, data=params, timeout=10)
                result = response.json()
                
                flag = result.get('flag', 'unknown')
                message = result.get('message', '')
                
                if flag == 'success':
                    print(f"✓ 成功: {message}")
                else:
                    print(f"✗ 失败: {message}")
                    
            except Exception as e:
                print(f"✗ 异常: {e}")
    
    def sign_method_1(self, params):
        """当前算法：secret + params + secret"""
        sorted_params = sorted(params.items())
        param_str = ''.join([f"{k}{v}" for k, v in sorted_params])
        sign_str = self.APP_SECRET + param_str + self.APP_SECRET
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
    
    def sign_method_2(self, params):
        """算法2：params + secret"""
        sorted_params = sorted(params.items())
        param_str = ''.join([f"{k}{v}" for k, v in sorted_params])
        sign_str = param_str + self.APP_SECRET
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
    
    def sign_method_3(self, params):
        """算法3：secret + params"""
        sorted_params = sorted(params.items())
        param_str = ''.join([f"{k}{v}" for k, v in sorted_params])
        sign_str = self.APP_SECRET + param_str
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
    
    def sign_method_4(self, params):
        """算法4：参数用&连接 + secret"""
        sorted_params = sorted(params.items())
        param_str = '&'.join([f"{k}={v}" for k, v in sorted_params])
        sign_str = param_str + '&key=' + self.APP_SECRET
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
    
    def sign_method_5(self, params):
        """算法5：小写MD5"""
        sorted_params = sorted(params.items())
        param_str = ''.join([f"{k}{v}" for k, v in sorted_params])
        sign_str = self.APP_SECRET + param_str + self.APP_SECRET
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().lower()
    
    def sign_method_6(self, params):
        """算法6：参数=值&格式"""
        sorted_params = sorted(params.items())
        param_str = '&'.join([f"{k}={v}" for k, v in sorted_params])
        sign_str = self.APP_SECRET + param_str + self.APP_SECRET
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
    
    def test_parameter_encoding(self):
        """测试参数编码"""
        print("\n\n测试参数编码")
        print("=" * 50)
        
        # 测试不同的参数编码方式
        test_cases = [
            {
                'name': '中文参数（原始）',
                'owner_no': '北区京东POP店'
            },
            {
                'name': '中文参数（URL编码）',
                'owner_no': urllib.parse.quote('北区京东POP店')
            },
            {
                'name': '英文参数',
                'owner_no': 'test_owner'
            },
            {
                'name': '空参数',
                'owner_no': ''
            }
        ]
        
        for case in test_cases:
            print(f"\n{case['name']}:")
            print(f"参数值: {case['owner_no']}")
            
            params = {
                'sid': self.SID,
                'appkey': self.APP_KEY,
                'timestamp': str(int(time.time())),
                'method': 'goods.spec.query.step',
                'format': 'json',
                'v': '1.0',
                'owner_no': case['owner_no']
            }
            
            # 使用当前签名算法
            sign = self.sign_method_1(params)
            params['sign'] = sign
            
            try:
                response = requests.post(self.API_URL, data=params, timeout=10)
                result = response.json()
                
                flag = result.get('flag', 'unknown')
                message = result.get('message', '')
                
                if flag == 'success':
                    print(f"✓ 成功: {message}")
                else:
                    print(f"✗ 失败: {message}")
                    
            except Exception as e:
                print(f"✗ 异常: {e}")
    
    def test_timestamp_formats(self):
        """测试时间戳格式"""
        print("\n\n测试时间戳格式")
        print("=" * 50)
        
        now = time.time()
        timestamp_formats = [
            {
                'name': '秒级时间戳',
                'timestamp': str(int(now))
            },
            {
                'name': '毫秒时间戳',
                'timestamp': str(int(now * 1000))
            },
            {
                'name': '浮点时间戳',
                'timestamp': f"{now:.3f}"
            },
            {
                'name': '整数毫秒',
                'timestamp': str(int(now * 1000))
            }
        ]
        
        for fmt in timestamp_formats:
            print(f"\n{fmt['name']}:")
            print(f"时间戳: {fmt['timestamp']}")
            
            params = {
                'sid': self.SID,
                'appkey': self.APP_KEY,
                'timestamp': fmt['timestamp'],
                'method': 'goods.spec.query.step',
                'format': 'json',
                'v': '1.0',
                'owner_no': 'test'
            }
            
            # 使用当前签名算法
            sign = self.sign_method_1(params)
            params['sign'] = sign
            
            try:
                response = requests.post(self.API_URL, data=params, timeout=10)
                result = response.json()
                
                flag = result.get('flag', 'unknown')
                message = result.get('message', '')
                
                if flag == 'success':
                    print(f"✓ 成功: {message}")
                else:
                    print(f"✗ 失败: {message}")
                    
            except Exception as e:
                print(f"✗ 异常: {e}")
    
    def test_minimal_request(self):
        """测试最小化请求"""
        print("\n\n测试最小化请求")
        print("=" * 50)
        
        # 只包含最基本的参数
        minimal_params = {
            'sid': self.SID,
            'appkey': self.APP_KEY,
            'timestamp': str(int(time.time())),
            'method': 'goods.spec.query.step'
        }
        
        print("最小化参数:")
        for k, v in minimal_params.items():
            print(f"  {k}: {v}")
        
        # 使用当前签名算法
        sign = self.sign_method_1(minimal_params)
        minimal_params['sign'] = sign
        
        print(f"\n生成的签名: {sign}")
        
        try:
            response = requests.post(self.API_URL, data=minimal_params, timeout=10)
            result = response.json()
            
            print(f"\n完整响应:")
            print(f"  flag: {result.get('flag')}")
            print(f"  code: {result.get('code', '')}")
            print(f"  message: {result.get('message', '')}")
            
            if result.get('data'):
                print(f"  data: {result.get('data')}")
                
        except Exception as e:
            print(f"\n异常: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("签名算法和参数测试")
        print("=" * 60)
        
        # 1. 测试签名算法
        self.test_signature_algorithms()
        
        # 2. 测试参数编码
        self.test_parameter_encoding()
        
        # 3. 测试时间戳格式
        self.test_timestamp_formats()
        
        # 4. 测试最小化请求
        self.test_minimal_request()
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("\n如果所有测试都失败，建议:")
        print("1. 联系技术支持确认正确的签名算法")
        print("2. 检查API密钥权限")
        print("3. 确认API服务状态")


def main():
    """主函数"""
    tester = SignatureTest()
    tester.run_all_tests()


if __name__ == '__main__':
    main()
