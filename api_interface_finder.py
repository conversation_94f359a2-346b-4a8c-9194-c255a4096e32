#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口名称查找工具
尝试多种可能的接口名称，找到可用的接口
"""

import hashlib
import time
import requests
import json
from datetime import datetime, timedelta

class APIInterfaceFinder:
    """API接口查找器"""
    
    def __init__(self):
        self.SID = 'changhe'
        self.APP_KEY = 'changhe_chycchsjcjgj_wdt'
        self.APP_SECRET = '72a70a3bedc51497a2a3b9c229d7df69'
        self.API_URL = 'https://openapi.wdtwms.com/open_api/service.php'
    
    def generate_sign(self, params):
        """生成API签名"""
        sorted_params = sorted(params.items())
        param_str = ''.join([f"{k}{v}" for k, v in sorted_params])
        sign_str = self.APP_SECRET + param_str + self.APP_SECRET
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
    
    def test_interface(self, method_name, params):
        """测试单个接口"""
        common_params = {
            'sid': self.SID,
            'appkey': self.APP_KEY,
            'timestamp': str(int(time.time())),
            'method': method_name,
            'format': 'json',
            'v': '1.0'
        }
        
        all_params = {**common_params, **params}
        all_params['sign'] = self.generate_sign(all_params)
        
        try:
            response = requests.post(self.API_URL, data=all_params, timeout=10)
            response.raise_for_status()
            result = response.json()
            
            return {
                'method': method_name,
                'success': result.get('flag') == 'success',
                'flag': result.get('flag'),
                'message': result.get('message', ''),
                'code': result.get('code', ''),
                'data_keys': list(result.get('data', {}).keys()) if result.get('data') else [],
                'content_count': len(result.get('content', [])) if result.get('content') else 0
            }
        except Exception as e:
            return {
                'method': method_name,
                'success': False,
                'flag': 'error',
                'message': str(e),
                'code': '',
                'data_keys': [],
                'content_count': 0
            }
    
    def find_goods_interfaces(self):
        """查找货品相关接口"""
        print("查找货品相关API接口")
        print("=" * 60)
        
        # 生成测试时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
        end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
        
        # 可能的接口名称列表
        interface_candidates = [
            # 原始提供的接口名称
            'goods.spec.query.step',
            
            # 可能的变体
            'goods.spec.query',
            'goods.query.step',
            'goods.query',
            'goods.list',
            'goods.search',
            'goods.get',
            'goods.find',
            
            # 规格相关
            'spec.query.step',
            'spec.query',
            'spec.list',
            'spec.search',
            
            # 档案相关
            'goods.archive.query',
            'goods.info.query',
            'goods.detail.query',
            'goods.master.query',
            
            # WMS相关
            'wms.goods.query',
            'wms.spec.query',
            'wms.goods.list',
            
            # 其他可能的格式
            'goods_spec_query_step',
            'goods_query',
            'goodsQuery',
            'specQuery',
            
            # 版本相关
            'goods.spec.query.v2',
            'goods.spec.query.v1',
            'goods.query.v2',
            'goods.query.v1'
        ]
        
        # 测试参数组合
        test_params_sets = [
            # 时间范围参数（推荐的格式）
            {
                'owner_no': '北区京东POP店',
                'start_time': start_time_str,
                'end_time': end_time_str,
                'page_no': '0',
                'page_size': '10'
            },
            # 传统分页参数
            {
                'owner_no': '北区京东POP店',
                'page_no': '1',
                'page_size': '10'
            },
            # 简化参数
            {
                'owner_no': '北区京东POP店'
            }
        ]
        
        successful_interfaces = []
        
        for i, params in enumerate(test_params_sets):
            print(f"\n参数组合 {i+1}: {list(params.keys())}")
            print("-" * 40)
            
            for method in interface_candidates:
                result = self.test_interface(method, params)
                
                if result['success']:
                    print(f"✓ {method:<25} - 成功!")
                    if result['content_count'] > 0:
                        print(f"  └─ 返回 {result['content_count']} 条数据")
                    elif result['data_keys']:
                        print(f"  └─ 数据字段: {result['data_keys']}")
                    successful_interfaces.append((method, params, result))
                    
                elif 'invalid-argument' in result['code'] or '名字错误' in result['message']:
                    print(f"✗ {method:<25} - 接口名称错误")
                elif 'permission' in result['message'].lower() or '权限' in result['message']:
                    print(f"⚠ {method:<25} - 权限问题: {result['message']}")
                elif 'not found' in result['message'].lower() or '不存在' in result['message']:
                    print(f"✗ {method:<25} - 接口不存在")
                else:
                    print(f"? {method:<25} - {result['flag']}: {result['message'][:50]}")
        
        return successful_interfaces
    
    def test_known_interfaces(self):
        """测试一些已知可能工作的接口"""
        print(f"\n\n测试已知接口类型")
        print("=" * 60)
        
        # 一些通用的测试接口
        test_interfaces = [
            ('system.time', {}),  # 系统时间接口
            ('user.info', {}),    # 用户信息接口
            ('api.version', {}),  # API版本接口
        ]
        
        for method, params in test_interfaces:
            result = self.test_interface(method, params)
            if result['success']:
                print(f"✓ {method} - 可用")
            else:
                print(f"✗ {method} - {result['message']}")
    
    def generate_config_update(self, successful_interfaces):
        """生成配置更新建议"""
        if not successful_interfaces:
            print(f"\n\n❌ 未找到可用的货品查询接口")
            print("建议:")
            print("1. 联系API提供方确认正确的接口名称")
            print("2. 检查API密钥权限")
            print("3. 确认接口是否已部署")
            return
        
        print(f"\n\n🎉 找到 {len(successful_interfaces)} 个可用接口!")
        print("=" * 60)
        
        for i, (method, params, result) in enumerate(successful_interfaces):
            print(f"\n接口 {i+1}: {method}")
            print(f"  参数: {list(params.keys())}")
            print(f"  返回数据: {result['content_count']} 条记录")
            if result['data_keys']:
                print(f"  数据字段: {result['data_keys']}")
        
        # 推荐最佳接口
        best_interface = successful_interfaces[0]
        method, params, result = best_interface
        
        print(f"\n推荐使用: {method}")
        print(f"更新 goods_api_client.py 第99行:")
        print(f"  return self._make_request('{method}', params)")
        
        # 生成更新脚本
        update_script = f"""
# 自动更新脚本
import re

def update_api_method():
    with open('goods_api_client.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 更新接口名称
    content = re.sub(
        r"return self\\._make_request\\('.*?', params\\)",
        f"return self._make_request('{method}', params)",
        content
    )
    
    with open('goods_api_client.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已更新接口名称为: {method}")

if __name__ == '__main__':
    update_api_method()
"""
        
        with open('update_api_method.py', 'w', encoding='utf-8') as f:
            f.write(update_script)
        
        print(f"\n已生成自动更新脚本: update_api_method.py")
        print(f"运行 'python update_api_method.py' 自动更新接口名称")

def main():
    """主函数"""
    finder = APIInterfaceFinder()
    
    # 查找货品接口
    successful_interfaces = finder.find_goods_interfaces()
    
    # 测试已知接口
    finder.test_known_interfaces()
    
    # 生成配置更新建议
    finder.generate_config_update(successful_interfaces)
    
    print(f"\n" + "=" * 60)
    print("查找完成")

if __name__ == '__main__':
    main()
