#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度诊断工具 - 分析goods.spec.query.step接口问题的其他原因
"""

import hashlib
import time
import requests
import json
import urllib.parse
from datetime import datetime, timedelta

class DeepDiagnosticTool:
    """深度诊断工具"""
    
    def __init__(self):
        self.SID = 'changhe'
        self.APP_KEY = 'changhe_chycchsjcjgj_wdt'
        self.APP_SECRET = '72a70a3bedc51497a2a3b9c229d7df69'
        self.API_URL = 'https://openapi.wdtwms.com/open_api/service.php'
    
    def generate_sign(self, params):
        """生成API签名"""
        sorted_params = sorted(params.items())
        param_str = ''.join([f"{k}{v}" for k, v in sorted_params])
        sign_str = self.APP_SECRET + param_str + self.APP_SECRET
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
    
    def test_1_parameter_encoding(self):
        """测试1：参数编码问题"""
        print("测试1：参数编码问题")
        print("-" * 40)
        
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
        end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
        
        # 测试不同的参数编码方式
        test_cases = [
            {
                'name': '标准参数',
                'params': {
                    'owner_no': '北区京东POP店',
                    'start_time': start_time_str,
                    'end_time': end_time_str,
                    'page_no': '0',
                    'page_size': '10'
                }
            },
            {
                'name': 'URL编码参数',
                'params': {
                    'owner_no': urllib.parse.quote('北区京东POP店'),
                    'start_time': start_time_str,
                    'end_time': end_time_str,
                    'page_no': '0',
                    'page_size': '10'
                }
            },
            {
                'name': '英文货主名',
                'params': {
                    'owner_no': 'test_owner',
                    'start_time': start_time_str,
                    'end_time': end_time_str,
                    'page_no': '0',
                    'page_size': '10'
                }
            }
        ]
        
        for case in test_cases:
            print(f"\n{case['name']}:")
            result = self._call_api('goods.spec.query.step', case['params'])
            print(f"  结果: {result.get('flag')} - {result.get('message', '')}")
    
    def test_2_timestamp_format(self):
        """测试2：时间戳格式问题"""
        print("\n\n测试2：时间戳格式问题")
        print("-" * 40)
        
        # 测试不同的时间戳格式
        now = int(time.time())
        test_timestamps = [
            str(now),           # 标准时间戳
            str(now * 1000),    # 毫秒时间戳
            f"{now}.000",       # 带小数点
        ]
        
        for ts in test_timestamps:
            print(f"\n时间戳格式: {ts}")
            
            params = {
                'sid': self.SID,
                'appkey': self.APP_KEY,
                'timestamp': ts,
                'method': 'goods.spec.query.step',
                'format': 'json',
                'v': '1.0',
                'owner_no': 'test'
            }
            
            params['sign'] = self.generate_sign(params)
            
            try:
                response = requests.post(self.API_URL, data=params, timeout=10)
                result = response.json()
                print(f"  结果: {result.get('flag')} - {result.get('message', '')}")
            except Exception as e:
                print(f"  异常: {e}")
    
    def test_3_api_version(self):
        """测试3：API版本问题"""
        print("\n\n测试3：API版本问题")
        print("-" * 40)
        
        versions = ['1.0', '2.0', '1.1', '']
        
        for version in versions:
            print(f"\nAPI版本: {version if version else '空'}")
            
            params = {
                'sid': self.SID,
                'appkey': self.APP_KEY,
                'timestamp': str(int(time.time())),
                'method': 'goods.spec.query.step',
                'format': 'json',
                'owner_no': 'test'
            }
            
            if version:
                params['v'] = version
            
            params['sign'] = self.generate_sign(params)
            
            try:
                response = requests.post(self.API_URL, data=params, timeout=10)
                result = response.json()
                print(f"  结果: {result.get('flag')} - {result.get('message', '')}")
            except Exception as e:
                print(f"  异常: {e}")
    
    def test_4_request_method(self):
        """测试4：请求方法问题"""
        print("\n\n测试4：请求方法问题")
        print("-" * 40)
        
        params = {
            'sid': self.SID,
            'appkey': self.APP_KEY,
            'timestamp': str(int(time.time())),
            'method': 'goods.spec.query.step',
            'format': 'json',
            'v': '1.0',
            'owner_no': 'test'
        }
        
        params['sign'] = self.generate_sign(params)
        
        # 测试POST请求
        print("POST请求:")
        try:
            response = requests.post(self.API_URL, data=params, timeout=10)
            result = response.json()
            print(f"  结果: {result.get('flag')} - {result.get('message', '')}")
        except Exception as e:
            print(f"  异常: {e}")
        
        # 测试GET请求
        print("\nGET请求:")
        try:
            response = requests.get(self.API_URL, params=params, timeout=10)
            result = response.json()
            print(f"  结果: {result.get('flag')} - {result.get('message', '')}")
        except Exception as e:
            print(f"  异常: {e}")
    
    def test_5_content_type(self):
        """测试5：Content-Type问题"""
        print("\n\n测试5：Content-Type问题")
        print("-" * 40)
        
        params = {
            'sid': self.SID,
            'appkey': self.APP_KEY,
            'timestamp': str(int(time.time())),
            'method': 'goods.spec.query.step',
            'format': 'json',
            'v': '1.0',
            'owner_no': 'test'
        }
        
        params['sign'] = self.generate_sign(params)
        
        content_types = [
            'application/x-www-form-urlencoded',
            'application/json',
            'multipart/form-data',
            None  # 不设置
        ]
        
        for ct in content_types:
            print(f"\nContent-Type: {ct if ct else '默认'}")
            
            headers = {}
            if ct:
                headers['Content-Type'] = ct
            
            try:
                if ct == 'application/json':
                    response = requests.post(self.API_URL, json=params, headers=headers, timeout=10)
                else:
                    response = requests.post(self.API_URL, data=params, headers=headers, timeout=10)
                
                result = response.json()
                print(f"  结果: {result.get('flag')} - {result.get('message', '')}")
            except Exception as e:
                print(f"  异常: {e}")
    
    def test_6_sign_algorithm(self):
        """测试6：签名算法问题"""
        print("\n\n测试6：签名算法问题")
        print("-" * 40)
        
        params = {
            'sid': self.SID,
            'appkey': self.APP_KEY,
            'timestamp': str(int(time.time())),
            'method': 'goods.spec.query.step',
            'format': 'json',
            'v': '1.0',
            'owner_no': 'test'
        }
        
        # 测试不同的签名算法
        print("标准签名算法:")
        sorted_params = sorted(params.items())
        param_str = ''.join([f"{k}{v}" for k, v in sorted_params])
        sign_str = self.APP_SECRET + param_str + self.APP_SECRET
        sign1 = hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
        print(f"  签名字符串: {sign_str}")
        print(f"  生成签名: {sign1}")
        
        # 测试不带secret前缀的签名
        print("\n不带前缀secret的签名:")
        sign_str2 = param_str + self.APP_SECRET
        sign2 = hashlib.md5(sign_str2.encode('utf-8')).hexdigest().upper()
        print(f"  签名字符串: {sign_str2}")
        print(f"  生成签名: {sign2}")
        
        # 测试不带secret后缀的签名
        print("\n不带后缀secret的签名:")
        sign_str3 = self.APP_SECRET + param_str
        sign3 = hashlib.md5(sign_str3.encode('utf-8')).hexdigest().upper()
        print(f"  签名字符串: {sign_str3}")
        print(f"  生成签名: {sign3}")
        
        # 测试小写签名
        print("\n小写签名:")
        sign4 = hashlib.md5(sign_str.encode('utf-8')).hexdigest().lower()
        print(f"  生成签名: {sign4}")
        
        # 分别测试这些签名
        for i, sign in enumerate([sign1, sign2, sign3, sign4], 1):
            test_params = params.copy()
            test_params['sign'] = sign
            
            try:
                response = requests.post(self.API_URL, data=test_params, timeout=10)
                result = response.json()
                print(f"\n签名{i}测试结果: {result.get('flag')} - {result.get('message', '')}")
            except Exception as e:
                print(f"\n签名{i}测试异常: {e}")
    
    def test_7_minimal_request(self):
        """测试7：最小化请求"""
        print("\n\n测试7：最小化请求")
        print("-" * 40)
        
        # 只包含必要参数
        minimal_params = {
            'sid': self.SID,
            'appkey': self.APP_KEY,
            'timestamp': str(int(time.time())),
            'method': 'goods.spec.query.step'
        }
        
        minimal_params['sign'] = self.generate_sign(minimal_params)
        
        print("最小化参数:")
        for k, v in minimal_params.items():
            print(f"  {k}: {v}")
        
        try:
            response = requests.post(self.API_URL, data=minimal_params, timeout=10)
            result = response.json()
            print(f"\n结果: {result.get('flag')} - {result.get('message', '')}")
            print(f"完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        except Exception as e:
            print(f"\n异常: {e}")
    
    def _call_api(self, method, business_params):
        """调用API的通用方法"""
        common_params = {
            'sid': self.SID,
            'appkey': self.APP_KEY,
            'timestamp': str(int(time.time())),
            'method': method,
            'format': 'json',
            'v': '1.0'
        }
        
        all_params = {**common_params, **business_params}
        all_params['sign'] = self.generate_sign(all_params)
        
        try:
            response = requests.post(self.API_URL, data=all_params, timeout=10)
            return response.json()
        except Exception as e:
            return {'flag': 'error', 'message': str(e)}
    
    def run_all_tests(self):
        """运行所有测试"""
        print("深度诊断 - goods.spec.query.step 接口问题")
        print("=" * 60)
        
        tests = [
            self.test_1_parameter_encoding,
            self.test_2_timestamp_format,
            self.test_3_api_version,
            self.test_4_request_method,
            self.test_5_content_type,
            self.test_6_sign_algorithm,
            self.test_7_minimal_request
        ]
        
        for test in tests:
            try:
                test()
            except Exception as e:
                print(f"测试异常: {e}")
        
        print("\n" + "=" * 60)
        print("诊断完成")
        print("\n可能的解决方案:")
        print("1. 检查参数编码是否正确")
        print("2. 确认时间戳格式")
        print("3. 验证API版本号")
        print("4. 检查请求方法和Content-Type")
        print("5. 验证签名算法")
        print("6. 尝试最小化参数请求")


def main():
    """主函数"""
    diagnostic = DeepDiagnosticTool()
    diagnostic.run_all_tests()


if __name__ == '__main__':
    main()
