# 货品档案API同步工具 - 简化版使用说明

## 概述

这是一个专门用于从API获取货品信息并写入Excel文档的Python工具。简化版只支持单向同步（API → Excel），去除了复杂的双向同步功能，使用更简单。

## 主要文件

| 文件名 | 说明 |
|--------|------|
| `api_to_excel.py` | **主程序**（推荐使用） |
| `goods_api_client.py` | API客户端和Excel管理器 |
| `config.py` | 配置文件 |
| `货品档案.xlsx` | Excel数据文件 |
| `goods_api.log` | 日志文件 |

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 基本使用

#### 同步所有货主的数据
```bash
python api_to_excel.py
```

#### 同步指定货主的数据
```bash
python api_to_excel.py --owners "北区京东POP店" "七彩虹天猫店"
```

#### 查看配置信息
```bash
python api_to_excel.py --config-check
```

#### 查看当前数据摘要
```bash
python api_to_excel.py --summary
```

#### 查看帮助信息
```bash
python api_to_excel.py --help
```

## 功能特性

### ✅ 核心功能
- 📥 从API获取货品信息
- 📊 写入Excel文档
- 🔄 增量更新（更新现有记录，添加新记录）
- 💾 自动备份Excel文件
- 📝 详细日志记录
- 🔁 自动重试机制
- ⚙️ 灵活配置管理

### 📊 支持的数据字段

#### 基本信息
- 货主、货品编号、货品名称

#### 尺寸信息
- 长度(cm)、宽度(cm)、高度(cm)、体积(cm³)

#### 重量信息
- 毛重(kg)、箱重量(kg)

#### 包装信息
- 基本单位、每箱数量、箱体积(cm³)

#### 保质期信息
- 保质期天数、临期天数

#### 其他信息
- 条形码

## 配置说明

### API配置
程序使用以下默认配置：
```python
SID = 'changhe'
APP_KEY = 'changhe_chycchsjcjgj_wdt'
APP_SECRET = '72a70a3bedc51497a2a3b9c229d7df69'
API_URL = 'https://openapi.wdtwms.com/open_api/service.php'
```

### 环境变量配置（可选）
可以通过环境变量覆盖默认配置：
```bash
set WDT_SID=your_sid
set WDT_APP_KEY=your_app_key
set WDT_APP_SECRET=your_app_secret
set WDT_API_URL=your_api_url
```

## 使用示例

### 示例1：同步所有数据
```bash
python api_to_excel.py
```
输出：
```
同步完成！
  更新: 150条
  新增: 25条
  错误: 0个
```

### 示例2：同步指定货主
```bash
python api_to_excel.py --owners "北区京东POP店"
```

### 示例3：查看数据摘要
```bash
python api_to_excel.py --summary
```
输出：
```
数据摘要:
  总货品数量: 1750
  包含API数据的货品: 175
  货主分布:
    七彩虹天猫店: 758条
    北区京东POP店: 648条
    七彩虹新零售: 287条
    七彩虹速卖通: 52条
```

## 工作流程

1. **初始化**：加载配置，初始化API客户端和Excel管理器
2. **获取货主列表**：从Excel或参数获取需要处理的货主
3. **API调用**：分页获取每个货主的货品数据
4. **数据处理**：将API数据映射到Excel字段
5. **更新Excel**：增量更新现有数据，添加新数据
6. **保存文件**：自动备份原文件，保存新数据

## 日志说明

程序会生成详细的日志文件 `goods_api.log`，包含：

- API请求和响应信息
- 数据处理过程
- 错误和警告信息
- 统计结果

示例日志：
```
2025-08-04 19:46:08,078 - INFO - 成功加载Excel文件: 货品档案.xlsx, 共1748行数据
2025-08-04 19:46:08,653 - INFO - API客户端初始化完成 - SID: changhe
2025-08-04 19:46:08,741 - INFO - 发送API请求: goods.query
2025-08-04 19:46:09,017 - INFO - API响应: success
2025-08-04 19:46:09,198 - INFO - 数据更新完成 - 更新: 0条, 新增: 2条
```

## 错误处理

### 常见问题及解决方案

#### 1. API认证失败
**错误信息**：`API响应: failure`，`接口名字错误`
**解决方案**：
- 检查API配置是否正确
- 确认API接口名称和权限
- 联系API提供方确认接口状态

#### 2. Excel文件被占用
**错误信息**：`保存Excel文件失败`
**解决方案**：
- 关闭Excel程序
- 检查文件权限

#### 3. 网络连接问题
**错误信息**：`API请求失败`
**解决方案**：
- 检查网络连接
- 确认API服务器地址

#### 4. 数据格式问题
**错误信息**：`处理货品数据失败`
**解决方案**：
- 查看详细日志
- 检查API返回数据格式

## 性能说明

- **分页处理**：支持大量数据的分页获取
- **增量更新**：只更新变化的数据，提高效率
- **重试机制**：自动重试失败的API调用
- **内存优化**：适合处理大型Excel文件

## 安全注意事项

1. **API密钥安全**：不要在代码中硬编码密钥
2. **数据备份**：程序会自动备份，建议定期手动备份
3. **权限控制**：确保API账号只有必要的权限
4. **日志安全**：日志文件可能包含敏感信息

## 技术支持

如遇问题，请按以下步骤排查：

1. **查看日志**：检查 `goods_api.log` 文件
2. **检查配置**：运行 `python api_to_excel.py --config-check`
3. **查看摘要**：运行 `python api_to_excel.py --summary`
4. **测试网络**：确认能访问API服务器
5. **联系支持**：提供日志文件和错误信息

## 版本信息

- **版本**：v1.0.0 简化版
- **更新日期**：2025-08-04
- **支持的Python版本**：3.7+
- **依赖包**：requests, pandas, openpyxl

## 更新日志

### v1.0.0 简化版 (2025-08-04)
- 移除双向同步功能
- 简化配置和使用方式
- 优化错误处理
- 添加数据摘要功能
- 改进日志记录
