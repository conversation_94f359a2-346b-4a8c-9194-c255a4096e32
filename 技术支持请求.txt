
主题：goods.spec.query.step 接口部署状态咨询

您好，

我们在使用 goods.spec.query.step 接口时遇到问题，需要技术支持。

== 基本信息 ==
接口名称：goods.spec.query.step
API密钥：changhe_chycchsjcjgj_wdt
SID：changhe
API地址：https://openapi.wdtwms.com/open_api/service.php

== 错误信息 ==
响应状态：failure
错误代码：client.protocol.invalid-argument
错误消息：接口【】名字错误

== 请求参数示例 ==
owner_no: 北区京东POP店
start_time: 2025-08-04 20:14:03
end_time: 2025-08-04 21:14:03
page_no: 0
page_size: 10

== 问题咨询 ==
1. 该接口是否已在我们的环境中部署？
2. 我们的API密钥是否有访问该接口的权限？
3. 是否需要特殊的配置或开通流程？
4. 预计什么时候可以解决？
5. 是否有替代的接口可以获取货品档案数据？

== 业务需求 ==
我们需要通过API获取WMS的单品档案资料，用于货品信息同步。
这是我们业务系统的核心功能，希望能尽快解决。

== 联系方式 ==
请回复此邮件或提供其他联系方式。

谢谢！
