#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

import os

# API配置
WDT_CONFIG = {
    'SID': 'changhe',
    'APP_KEY': 'changhe_chycchsjcjgj_wdt',
    'APP_SECRET': '72a70a3bedc51497a2a3b9c229d7df69',
    'API_URL': 'https://openapi.wdtwms.com/open_api/service.php'
}

# Excel配置
EXCEL_CONFIG = {
    'FILE_PATH': '货品档案.xlsx',
    'BACKUP_ENABLED': True,
    'ENCODING': 'utf-8'
}

# 日志配置
LOG_CONFIG = {
    'LEVEL': 'INFO',
    'FILE_PATH': 'goods_api.log',
    'FORMAT': '%(asctime)s - %(levelname)s - %(message)s'
}

# 请求配置
REQUEST_CONFIG = {
    'TIMEOUT': 30,
    'PAGE_SIZE': 100,
    'MAX_RETRIES': 3,
    'RETRY_DELAY': 1  # 秒
}

# 字段映射配置 - API到Excel单向映射（基于goods.spec.query.step接口）
API_TO_EXCEL_MAPPING = {
    'owner_no': '货主',
    'spec_no': '货品编号',
    'goods_no': '货品编号(系统)',
    'goods_name': '货品名称',
    'spec_name': '规格名称',
    'length': '长度(cm)',
    'width': '宽度(cm)',
    'height': '高度(cm)',
    'volume': '体积(ml)',
    'gross_weight': '毛重(kg)',
    'base_unit': '基本单位',
    'unit_ratio': '箱规',
    'aux_unit_ratio': '每包数量',
    'box_weight': '箱重量(kg)',
    'box_volume': '箱体积(ml)',
    'validity_days': '保质期天数',
    'expire_days': '临期天数',
    'receive_days': '最佳收货天数',
    'barcode': '主条码',
    'brand_name': '品牌名称',
    'class_name': '分类名称',
    'short_name': '货品简称',
    'price': '价格',
    'remark': '备注',
    'img_url': '图片URL',
    'goods_type': '货品类别',
    'is_sn_enable': '序列号启用',
    'pick_score': '拣货积分',
    'pack_score': '打包积分',
    'examine_score': '验货积分',
    'stock_in_score': '入库积分',
    'putaway_score': '上架积分',
    'spec_prop1': '自定义属性1',
    'spec_prop2': '自定义属性2',
    'spec_prop3': '自定义属性3',
    'spec_prop4': '自定义属性4',
    'spec_prop5': '自定义属性5',
    'spec_prop6': '自定义属性6'
}

# Excel列定义（包含原有列和新增API字段）
EXCEL_COLUMNS = [
    # 原有列
    '货主', '货品编号', '货品名称', '备注', '修改时间', '货品简称',
    '货品别名', '分类编号', '分类名称', '品牌', '货品类别', '产地',
    '标记名称', '自定义属性1', '自定义属性2', '自定义属性3',
    '自定义属性4', '自定义属性5', '自定义属性6', '创建时间',
    # API新增字段
    '货品编号(系统)', '规格名称', '长度(cm)', '宽度(cm)', '高度(cm)',
    '体积(ml)', '毛重(kg)', '基本单位', '箱规', '每包数量',
    '箱重量(kg)', '箱体积(ml)', '保质期天数', '临期天数', '最佳收货天数',
    '主条码', '品牌名称', '价格', '图片URL', '序列号启用',
    '拣货积分', '打包积分', '验货积分', '入库积分', '上架积分'
]

def get_config():
    """获取配置信息"""
    config = {
        'wdt': WDT_CONFIG.copy(),
        'excel': EXCEL_CONFIG.copy(),
        'log': LOG_CONFIG.copy(),
        'request': REQUEST_CONFIG.copy(),
        'api_to_excel_mapping': API_TO_EXCEL_MAPPING.copy(),
        'excel_columns': EXCEL_COLUMNS.copy()
    }

    # 从环境变量覆盖配置
    for key, value in WDT_CONFIG.items():
        env_key = f'WDT_{key}'
        if env_key in os.environ:
            config['wdt'][key] = os.environ[env_key]

    return config
