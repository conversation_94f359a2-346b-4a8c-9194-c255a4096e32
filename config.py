#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

import os

# API配置
WDT_CONFIG = {
    'SID': 'changhe',
    'APP_KEY': 'changhe_chycchsjcjgj_wdt',
    'APP_SECRET': '72a70a3bedc51497a2a3b9c229d7df69',
    'API_URL': 'https://openapi.wdtwms.com/open_api/service.php'
}

# Excel配置
EXCEL_CONFIG = {
    'FILE_PATH': '货品档案.xlsx',
    'BACKUP_ENABLED': True,
    'ENCODING': 'utf-8'
}

# 日志配置
LOG_CONFIG = {
    'LEVEL': 'INFO',
    'FILE_PATH': 'goods_api.log',
    'FORMAT': '%(asctime)s - %(levelname)s - %(message)s'
}

# 请求配置
REQUEST_CONFIG = {
    'TIMEOUT': 30,
    'PAGE_SIZE': 100,
    'MAX_RETRIES': 3,
    'RETRY_DELAY': 1  # 秒
}

# 字段映射配置 - API到Excel单向映射
API_TO_EXCEL_MAPPING = {
    'owner_no': '货主',
    'spec_no': '货品编号',
    'goods_name': '货品名称',
    'length': '长度(cm)',
    'width': '宽度(cm)',
    'height': '高度(cm)',
    'volume': '体积(cm³)',
    'gross_weight': '毛重(kg)',
    'base_unit': '基本单位',
    'unit_ratio': '每箱数量',
    'box_weight': '箱重量(kg)',
    'box_volume': '箱体积(cm³)',
    'validity_days': '保质期天数',
    'expire_days': '临期天数',
    'barcode': '条形码'
}

# Excel列定义
EXCEL_COLUMNS = [
    '货主', '货品编号', '货品名称', '备注', '修改时间', '货品简称',
    '货品别名', '分类编号', '分类名称', '品牌', '货品类别', '产地',
    '标记名称', '自定义属性1', '自定义属性2', '自定义属性3',
    '自定义属性4', '自定义属性5', '自定义属性6', '创建时间',
    '长度(cm)', '宽度(cm)', '高度(cm)', '体积(cm³)', '毛重(kg)',
    '基本单位', '每箱数量', '箱重量(kg)', '箱体积(cm³)', '保质期天数', 
    '临期天数', '条形码'
]

def get_config():
    """获取配置信息"""
    config = {
        'wdt': WDT_CONFIG.copy(),
        'excel': EXCEL_CONFIG.copy(),
        'log': LOG_CONFIG.copy(),
        'request': REQUEST_CONFIG.copy(),
        'api_to_excel_mapping': API_TO_EXCEL_MAPPING.copy(),
        'excel_columns': EXCEL_COLUMNS.copy()
    }

    # 从环境变量覆盖配置
    for key, value in WDT_CONFIG.items():
        env_key = f'WDT_{key}'
        if env_key in os.environ:
            config['wdt'][key] = os.environ[env_key]

    return config
